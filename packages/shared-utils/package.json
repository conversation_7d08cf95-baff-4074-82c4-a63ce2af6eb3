{"name": "shared-utils", "version": "0.0.0", "private": true, "type": "module", "license": "MIT", "sideEffects": false, "scripts": {"cy:open": "cypress open", "cy:run": "cypress run --component --headed --browser chrome"}, "dependencies": {"@graphql-typed-document-node/core": "*", "@sentry/nextjs": "*", "@tanstack/react-query": "^4.28.0", "@unleash/nextjs": "*", "age-encryption": "^0.1.5", "axios": "^1.3.5", "class-variance-authority": "^0.6.0", "cookies-next": "^2.1.1", "date-fns": "^2.29.3", "date-fns-tz": "^1.3.8", "file-saver": "^2.0.5", "idb": "^8.0.0", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "jwt-encode": "^1.0.1", "numeral": "^2.0.6", "react": "*", "react-query-kit": "^2.0.7", "shared-assets": "workspace:*", "shared-config": "workspace:*", "socket.io": "*", "socket.io-client": "*", "tailwind-merge": "^2.5.4", "valid-filename": "^4.0.0", "winston": "*", "workbox-window": "^7.0.0", "zod": "*"}, "peerDependencies": {"next": "*"}, "devDependencies": {"@cypress/code-coverage": "*", "@types/file-saver": "^2.0.5", "@types/jsonwebtoken": "^9.0.2", "@types/jwt-encode": "1.0.3", "@types/numeral": "^2.0.2", "@types/react": "^18.2.14", "@types/socket.io-client": "*", "cypress": "*"}}