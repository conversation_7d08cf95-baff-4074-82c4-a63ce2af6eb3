import { cookies } from "next/headers"
import { getAccessTokenHeader, getAllAccessToken } from "../accessToken"
import { EXPIRED_TIME_TOKEN_COOKIE_NAME, TAppsName } from "../authentication"
import { getUnixTime } from "date-fns"

type TRequest = {
  headers: Record<string, string>
  url: string
}
type TGraphqlRscMiddleware = {
  request: TRequest
  getServerSession: () => Promise<{ token: { accessToken: string } } | null>
}

const getNewestToken = async (
  getServerSession: () => Promise<{ token: { accessToken: string } } | null>
) => {
  const expTimeToken = parseInt(
    cookies().get(EXPIRED_TIME_TOKEN_COOKIE_NAME)?.value ?? ""
  )

  const tokenCookies = getAccessTokenHeader()?.stringFormat || ""
  const currentTime = getUnixTime(new Date())
  const timeDifference = expTimeToken - currentTime
  const isOneMinuteDifference = timeDifference <= 60

  if (isOneMinuteDifference || !expTimeToken || !tokenCookies) {
    const newSession = await getServerSession()
    return `gtp.accessToken=${newSession?.token?.accessToken};`
  }

  return tokenCookies
}

const graphqlRscMiddleware = async ({
  getServerSession,
  request,
}: TGraphqlRscMiddleware) => {
  const token = await getNewestToken(getServerSession)

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Cookie: token || "",
  }

  request.headers = {
    ...headers,
    ...request.headers,
  }

  return request
}

export default graphqlRscMiddleware
