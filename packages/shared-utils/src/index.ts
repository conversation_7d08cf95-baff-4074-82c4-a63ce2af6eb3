export { default as copyTextToClipboard } from "./copyTextToClipboard"
export { download, downloadAndZip, downloadAndZipMultitermin } from "./file"
export { default as formatBytes } from "./formatBytes"
export { formatDate, formatNumber } from "./formatter"
export { default as generateBlurDataUrl } from "./generateBlurDataUrl"
export { default as rules } from "./rules"
export { default as scrollTop } from "./scrollTop"
export { default as sortAlphabetically } from "./sortAlphabetically"
export { default as getInitials } from "./getInitials"
export { redirectToCallbackPage, midtransErrorMapping } from "./payment"
export { default as axiosProgress } from "./axiosProgress"
export { default as generatePdfViewerUrl } from "./generatePdfViewerUrl"
export { default as capitalize } from "./capitalize"
export { default as formatPhoneNumberToInternational } from "./formatPhoneNumberToInternational"
export { default as toTitleCase } from "./toTitleCase"
export { default as formatSold } from "./formatSold"
export { default as checkBrowser } from "./checkBrowser"
export { default as validateImageDimensions } from "./validateImageDimensions"
export { default as checkPopupBlocker } from "./checkPopupBlocker"
export { default as checkPopupBlocked } from "./checkPopupBlocked"
export { default as delay } from "./delay"
export { default as limitNumericInputLength } from "./limitNumericInputLength"
export { default as generateNewTaxLabelForOrder } from "./generateNewTaxLabelForOrder"
export { default as toLowerCaseLocation } from "./toLowerCaseLocationName"
export { default as shouldNotRender } from "./shouldNotRender"
export { default as getUserTimezone } from "./getUserTimezone"
export { default as handleGenericError } from "./handleGenericError"
export { default as handleScrollLoadMore } from "./handleScrollLoadMore"
export { default as checkUnauthorized } from "./checkUnauthorized"
export { default as getResponseError } from "./getResponseError"
export {
  default as graphqlClientMiddleware,
  type TRequest,
} from "./graphqlClientMiddleware"
export { default as graphqlClientMiddlewareV2 } from "./graphqlClientMiddlewareV2"
export { default as getFlag, type FeatureFlag } from "./getFlag"
export { default as calculateFormattedTotalWeight } from "./calculateFormattedTotalWeight"
export { default as formatIntoCurrency } from "./formatIntoCurrency"
export { default as catchNonGenericError } from "./catchNonGenericError"
export { default as formatProvince } from "./formatProvince"
export { default as formatRegionName } from "./formatRegionName"
export { default as formatRupiah } from "./formatRupiah"
export { default as calculatePriceAfterTax } from "./calculatePriceAfterTax"
export { default as transformPPN11 } from "./transformPPN11"
export { default as calculateTaxValue } from "./calculateTaxValue"
export { default as generateTaxLabel } from "./generateTaxLabel"
export { default as cn } from "./cn"
export { default as convertWeight } from "./convertWeight"
export { default as formatWeight } from "./formatWeight"
export { default as formatDateDayTime } from "./formatDateDayTime"
export { default as getRequestArrivalDate } from "./getRequestArrivalDate"
export { formatBuyerAddress } from "./formatAddress"
export { scrollToFirstError } from "./scroll"
export { default as renderStockUnitTypeLabel } from "./renderStockUnitTypeLabel"
export { default as encryptUploadPayload } from "./encryptUploadPayload"
export { default as generateEncryptionKeys } from "./generateEncryptionKeys"
export { default as removeLastCharIfMatch } from "./removeLastCharIfMatch"
export { default as multiDownload } from "./multipleDownload"
export { default as decodeHTMLEntity } from "./decodeHTMLEntity"
export { default as decimalToFraction } from "./decimalToFraction"
export { default as getObjectKeys } from "./getObjectKeys"
export { default as extractGraphQLInfo } from "./extractGraphQLInfo"

// Validation
export { default as isValidFileName } from "./isValidFilename"
export { default as isValidUrl } from "./isValidUrl"
export { default as isValidNpwp } from "./isValidNpwp"
export { default as isObjectEmpty } from "./isObjectEmpty"
export { default as xssPreventionSchema } from "./xssPreventionSchema"
export { default as escapeHtmlTags } from "./escapeHtmlTags"

// Hooks
export { default as useClickOutside } from "./useClickOutside"
export { default as useForkRef } from "./useForkRef"
export { default as useScript } from "./useScript"
export { default as useDebounce } from "./useDebounce"
export { default as useUploadGcs } from "./useUploadGcs"
export { default as useUploadGcsV2 } from "./useUploadGcsV2"
export { default as useGetUploadStatus } from "./useGetUploadStatus"
export { default as useGetUploadStatusV2 } from "./useGetUploadStatusV2"
export { default as useViewportSize } from "./useViewportSize"
export { default as useInterval } from "./hooks/useInterval"
export { default as useBusy } from "./useBusy"
export { default as useDeepCompareEffect } from "./useDeepCompareEffect"
export { default as useUploadBySignedUrl } from "./hooks/useUploadBySignedUrl"
export { default as useCountdown } from "./hooks/useCountdown"
export { default as useWindowFocus } from "./useWindowFocus"
export { default as useDeepCompareMemo } from "./useDeepCompareMemo"
export { default as AnalyticsHead } from "./analytics/AnalyticsHead"
export { default as AnalyticsBody } from "./analytics/AnalyticsBody"

// Mini Competition
export { default as determineCompetitionItemCopywriting } from "./mini-competition/determineCompetitionItemCopywriting"

// Blacklist
export { default as formatBlacklistLetterheadInfo } from "./blacklist/formatLetterheadInfo"

// Logging
export { default as sendGcpLog, TWinstonLevel } from "./logging/sendGcpLog"
export { default as sentryGqlLogError } from "./logging/sentry/sentryGqlLogError"
export { default as transformNonGenericError } from "./transformNonGenericError"
