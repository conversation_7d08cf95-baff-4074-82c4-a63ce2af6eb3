export interface GraphQLRequest {
  query: string
  variables?: Record<string, any>
  operationName?: string
}

export interface GraphQLResponse {
  data?: any
  errors?: Array<{
    message: string
    locations?: Array<{
      line: number
      column: number
    }>
    path?: Array<string | number>
    extensions?: Record<string, any>
  }>
  extensions?: Record<string, any>
}

export interface CachedGraphQLEntry {
  query: string
  variables?: Record<string, any>
  operationName?: string
  response: {
    headers: Record<string, string>
    status: number
    statusText: string
    body: GraphQLResponse
  }
  timestamp: number
  hash: string
}

export interface GraphQLCacheConfig {
  dbName?: string
  storeName?: string
  maxAge?: number // in seconds, default 3600 (1 hour)
  maxEntries?: number // default 100
  enabled?: boolean // default true
  cacheableOperations?: string[] // operation names to cache, if empty cache all queries
  excludeOperations?: string[] // operation names to exclude from caching
}

export interface ServiceWorkerConfig {
  swUrl?: string
  scope?: string
  updateViaCache?: 'imports' | 'all' | 'none'
}

export type CacheStrategy = 'stale-while-revalidate' | 'cache-first' | 'network-first'

export interface WorkboxGraphQLConfig extends GraphQLCacheConfig {
  strategy?: CacheStrategy
  networkTimeoutSeconds?: number
}
