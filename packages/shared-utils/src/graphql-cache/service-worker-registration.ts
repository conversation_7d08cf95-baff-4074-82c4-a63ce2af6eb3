import { Workbox } from 'workbox-window'
import type { ServiceWorkerConfig } from './types'

export class GraphQLServiceWorkerManager {
  private wb: Workbox | null = null
  private config: ServiceWorkerConfig

  constructor(config: ServiceWorkerConfig = {}) {
    this.config = {
      swUrl: config.swUrl || '/sw.js',
      scope: config.scope || '/',
      updateViaCache: config.updateViaCache || 'none'
    }
  }

  async register(): Promise<ServiceWorkerRegistration | null> {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      console.warn('Service Worker not supported')
      return null
    }

    try {
      this.wb = new Workbox(this.config.swUrl, {
        scope: this.config.scope,
        updateViaCache: this.config.updateViaCache
      })

      // Add event listeners
      this.setupEventListeners()

      // Register the service worker
      const registration = await this.wb.register()
      console.log('GraphQL Service Worker registered successfully')
      
      return registration
    } catch (error) {
      console.error('GraphQL Service Worker registration failed:', error)
      return null
    }
  }

  private setupEventListeners(): void {
    if (!this.wb) return

    this.wb.addEventListener('installed', (event) => {
      console.log('GraphQL Service Worker installed', event)
    })

    this.wb.addEventListener('waiting', (event) => {
      console.log('GraphQL Service Worker waiting', event)
      // You might want to show a notification to the user here
      this.showUpdateAvailable()
    })

    this.wb.addEventListener('controlling', (event) => {
      console.log('GraphQL Service Worker controlling', event)
      // Reload the page to ensure all resources are served by the new SW
      window.location.reload()
    })

    this.wb.addEventListener('activated', (event) => {
      console.log('GraphQL Service Worker activated', event)
    })

    this.wb.addEventListener('redundant', (event) => {
      console.log('GraphQL Service Worker redundant', event)
    })

    this.wb.addEventListener('externalinstalled', (event) => {
      console.log('External GraphQL Service Worker installed', event)
    })

    this.wb.addEventListener('externalwaiting', (event) => {
      console.log('External GraphQL Service Worker waiting', event)
    })

    this.wb.addEventListener('externalactivated', (event) => {
      console.log('External GraphQL Service Worker activated', event)
    })
  }

  private showUpdateAvailable(): void {
    // You can customize this to show your own UI
    if (confirm('A new version is available. Reload to update?')) {
      this.skipWaiting()
    }
  }

  async skipWaiting(): Promise<void> {
    if (!this.wb) return

    try {
      // Tell the waiting SW to skip waiting and become active
      await this.wb.messageSkipWaiting()
    } catch (error) {
      console.error('Error skipping waiting:', error)
    }
  }

  async unregister(): Promise<boolean> {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      return false
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration(this.config.scope)
      if (registration) {
        const result = await registration.unregister()
        console.log('GraphQL Service Worker unregistered:', result)
        return result
      }
      return false
    } catch (error) {
      console.error('Error unregistering GraphQL Service Worker:', error)
      return false
    }
  }

  async update(): Promise<void> {
    if (!this.wb) return

    try {
      await this.wb.update()
      console.log('GraphQL Service Worker update check completed')
    } catch (error) {
      console.error('Error updating GraphQL Service Worker:', error)
    }
  }

  async getCacheStats(): Promise<{ count: number; size: number } | null> {
    if (!this.wb) return null

    try {
      // Send message to service worker to get cache stats
      const response = await this.wb.messageSW({ type: 'GET_CACHE_STATS' })
      return response
    } catch (error) {
      console.error('Error getting cache stats:', error)
      return null
    }
  }

  async clearCache(): Promise<boolean> {
    if (!this.wb) return false

    try {
      // Send message to service worker to clear cache
      const response = await this.wb.messageSW({ type: 'CLEAR_CACHE' })
      return response.success
    } catch (error) {
      console.error('Error clearing cache:', error)
      return false
    }
  }
}

// Convenience function for easy registration
export async function registerGraphQLServiceWorker(
  config: ServiceWorkerConfig = {}
): Promise<ServiceWorkerRegistration | null> {
  const manager = new GraphQLServiceWorkerManager(config)
  return await manager.register()
}

// React hook for service worker management
export function useGraphQLServiceWorker(config: ServiceWorkerConfig = {}) {
  const [manager] = useState(() => new GraphQLServiceWorkerManager(config))
  const [isRegistered, setIsRegistered] = useState(false)
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false)

  useEffect(() => {
    let mounted = true

    const registerSW = async () => {
      const registration = await manager.register()
      if (mounted) {
        setIsRegistered(!!registration)
      }
    }

    registerSW()

    return () => {
      mounted = false
    }
  }, [manager])

  const skipWaiting = useCallback(async () => {
    await manager.skipWaiting()
    setIsUpdateAvailable(false)
  }, [manager])

  const clearCache = useCallback(async () => {
    return await manager.clearCache()
  }, [manager])

  const getCacheStats = useCallback(async () => {
    return await manager.getCacheStats()
  }, [manager])

  return {
    isRegistered,
    isUpdateAvailable,
    skipWaiting,
    clearCache,
    getCacheStats,
    manager
  }
}

// Import React hooks if available
let useState: any, useEffect: any, useCallback: any
try {
  const React = require('react')
  useState = React.useState
  useEffect = React.useEffect
  useCallback = React.useCallback
} catch {
  // React not available, hooks will be undefined
}
