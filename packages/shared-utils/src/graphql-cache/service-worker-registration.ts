import type { ServiceWorkerConfig } from "./types"

// Import React hooks conditionally
let useState: any, useEffect: any, useCallback: any
try {
  const React = require("react")
  useState = React.useState
  useEffect = React.useEffect
  useCallback = React.useCallback
} catch {
  // React not available, hooks will be undefined
}

export class GraphQLServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null
  private config: ServiceWorkerConfig

  constructor(config: ServiceWorkerConfig = {}) {
    this.config = {
      swUrl: config.swUrl || "/sw.js",
      scope: config.scope || "/",
      updateViaCache: config.updateViaCache || "none",
    }
  }

  async register(): Promise<ServiceWorkerRegistration | null> {
    if (typeof window === "undefined" || !("serviceWorker" in navigator)) {
      console.warn("Service Worker not supported")
      return null
    }

    try {
      const registration = await navigator.serviceWorker.register(
        this.config.swUrl!,
        {
          scope: this.config.scope,
          updateViaCache: this.config.updateViaCache,
        }
      )

      this.registration = registration
      this.setupEventListeners()

      console.log("GraphQL Service Worker registered successfully")

      return registration
    } catch (error) {
      console.error("GraphQL Service Worker registration failed:", error)
      return null
    }
  }

  private setupEventListeners(): void {
    if (!this.registration) return

    // Listen for service worker updates
    this.registration.addEventListener("updatefound", () => {
      const newWorker = this.registration!.installing
      if (newWorker) {
        newWorker.addEventListener("statechange", () => {
          if (
            newWorker.state === "installed" &&
            navigator.serviceWorker.controller
          ) {
            console.log("GraphQL Service Worker update available")
            this.showUpdateAvailable()
          }
        })
      }
    })

    // Listen for controlling service worker changes
    navigator.serviceWorker.addEventListener("controllerchange", () => {
      console.log("GraphQL Service Worker controlling")
      window.location.reload()
    })
  }

  private showUpdateAvailable(): void {
    // You can customize this to show your own UI
    if (confirm("A new version is available. Reload to update?")) {
      this.skipWaiting()
    }
  }

  async skipWaiting(): Promise<void> {
    if (!this.registration) return

    try {
      // Tell the waiting SW to skip waiting and become active
      if (this.registration.waiting) {
        this.registration.waiting.postMessage({ type: "SKIP_WAITING" })
      }
    } catch (error) {
      console.error("Error skipping waiting:", error)
    }
  }

  async unregister(): Promise<boolean> {
    if (typeof window === "undefined" || !("serviceWorker" in navigator)) {
      return false
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration(
        this.config.scope
      )
      if (registration) {
        const result = await registration.unregister()
        console.log("GraphQL Service Worker unregistered:", result)
        return result
      }
      return false
    } catch (error) {
      console.error("Error unregistering GraphQL Service Worker:", error)
      return false
    }
  }

  async update(): Promise<void> {
    if (!this.registration) return

    try {
      await this.registration.update()
      console.log("GraphQL Service Worker update check completed")
    } catch (error) {
      console.error("Error updating GraphQL Service Worker:", error)
    }
  }

  async getCacheStats(): Promise<{ count: number; size: number } | null> {
    if (!this.registration || !this.registration.active) return null

    try {
      // Send message to service worker to get cache stats
      const messageChannel = new MessageChannel()

      return new Promise<{ count: number; size: number } | null>((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          const data = event.data as { count: number; size: number }
          resolve(data)
        }

        this.registration!.active!.postMessage({ type: "GET_CACHE_STATS" }, [
          messageChannel.port2,
        ])
      })
    } catch (error) {
      console.error("Error getting cache stats:", error)
      return null
    }
  }

  async clearCache(): Promise<boolean> {
    if (!this.registration || !this.registration.active) return false

    try {
      // Send message to service worker to clear cache
      const messageChannel = new MessageChannel()

      return new Promise<boolean>((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          const data = event.data as { success?: boolean }
          resolve(data.success || false)
        }

        this.registration!.active!.postMessage({ type: "CLEAR_CACHE" }, [
          messageChannel.port2,
        ])
      })
    } catch (error) {
      console.error("Error clearing cache:", error)
      return false
    }
  }
}

// Convenience function for easy registration
export async function registerGraphQLServiceWorker(
  config: ServiceWorkerConfig = {}
): Promise<ServiceWorkerRegistration | null> {
  const manager = new GraphQLServiceWorkerManager(config)
  return await manager.register()
}

// React hook for service worker management
export function useGraphQLServiceWorker(config: ServiceWorkerConfig = {}) {
  const [manager] = useState(() => new GraphQLServiceWorkerManager(config))
  const [isRegistered, setIsRegistered] = useState(false)
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false)

  useEffect(() => {
    let mounted = true

    const registerSW = async () => {
      const registration = await manager.register()
      if (mounted) {
        setIsRegistered(!!registration)
      }
    }

    registerSW()

    return () => {
      mounted = false
    }
  }, [manager])

  const skipWaiting = useCallback(async () => {
    await manager.skipWaiting()
    setIsUpdateAvailable(false)
  }, [manager])

  const clearCache = useCallback(async () => {
    return await manager.clearCache()
  }, [manager])

  const getCacheStats = useCallback(async () => {
    return await manager.getCacheStats()
  }, [manager])

  return {
    isRegistered,
    isUpdateAvailable,
    skipWaiting,
    clearCache,
    getCacheStats,
    manager,
  }
}
