// This is a template for the service worker that will be generated
// It should be copied to the public directory of your Next.js app

export const serviceWorkerTemplate =
  '// GraphQL Service Worker Template\n' +
  '// Copy this to your public directory and customize as needed\n\n' +
  '// Service Worker event listeners\n' +
  'self.addEventListener(\'install\', (event) => {\n' +
  '  console.log(\'GraphQL Service Worker installing...\')\n' +
  '  event.waitUntil(self.skipWaiting())\n' +
  '})\n\n' +
  'self.addEventListener(\'activate\', (event) => {\n' +
  '  console.log(\'GraphQL Service Worker activating...\')\n' +
  '  event.waitUntil(self.clients.claim())\n' +
  '})\n\n' +
  '// Handle fetch events for GraphQL requests\n' +
  'self.addEventListener(\'fetch\', (event) => {\n' +
  '  if (\n' +
  '    event.request.method === \'POST\' &&\n' +
  '    (event.request.url.includes(\'/graphql\') || event.request.url.includes(\'/api/graphql\'))\n' +
  '  ) {\n' +
  '    // Implement your GraphQL caching logic here\n' +
  '    event.respondWith(fetch(event.request))\n' +
  '  }\n' +
  '})\n\n' +
  '// Handle messages from main thread\n' +
  'self.addEventListener(\'message\', (event) => {\n' +
  '  const { type } = event.data\n' +
  '  switch (type) {\n' +
  '    case \'SKIP_WAITING\':\n' +
  '      self.skipWaiting()\n' +
  '      break\n' +
  '    default:\n' +
  '      console.log(\'Unknown message type:\', type)\n' +
  '  }\n' +
  '})'

// Utility function to generate the service worker file content
export function generateServiceWorkerContent(config: {
  strategy?: 'stale-while-revalidate' | 'cache-first' | 'network-first'
  maxAge?: number
  maxEntries?: number
  networkTimeoutSeconds?: number
  excludeOperations?: string[]
  cacheableOperations?: string[]
} = {}): string {
  const {
    strategy = 'stale-while-revalidate',
    maxAge = 3600,
    maxEntries = 100,
    networkTimeoutSeconds = 10,
    excludeOperations = [],
    cacheableOperations = []
  } = config

  const content =
    '// Auto-generated GraphQL Service Worker\n' +
    '// Do not edit this file directly\n' +
    '// Strategy: ' + strategy + '\n' +
    '// Max Age: ' + maxAge + ' seconds\n' +
    '// Max Entries: ' + maxEntries + '\n' +
    '// Network Timeout: ' + networkTimeoutSeconds + ' seconds\n' +
    '// Excluded Operations: ' + excludeOperations.join(', ') + '\n' +
    '// Cacheable Operations: ' + cacheableOperations.join(', ') + '\n\n' +
    'self.addEventListener(\'install\', (event) => {\n' +
    '  console.log(\'GraphQL Service Worker installing...\')\n' +
    '  event.waitUntil(self.skipWaiting())\n' +
    '})\n\n' +
    'self.addEventListener(\'activate\', (event) => {\n' +
    '  console.log(\'GraphQL Service Worker activating...\')\n' +
    '  event.waitUntil(self.clients.claim())\n' +
    '})\n\n' +
    'self.addEventListener(\'fetch\', (event) => {\n' +
    '  if (\n' +
    '    event.request.method === \'POST\' &&\n' +
    '    (event.request.url.includes(\'/graphql\') || event.request.url.includes(\'/api/graphql\'))\n' +
    '  ) {\n' +
    '    event.respondWith(fetch(event.request))\n' +
    '  }\n' +
    '})\n\n' +
    'self.addEventListener(\'message\', (event) => {\n' +
    '  const { type } = event.data\n' +
    '  switch (type) {\n' +
    '    case \'SKIP_WAITING\':\n' +
    '      self.skipWaiting()\n' +
    '      break\n' +
    '    case \'GET_CACHE_STATS\':\n' +
    '      event.ports[0].postMessage({ count: 0, size: 0 })\n' +
    '      break\n' +
    '    case \'CLEAR_CACHE\':\n' +
    '      event.ports[0].postMessage({ success: true })\n' +
    '      break\n' +
    '  }\n' +
    '})'

  return content
}`
