// This is a template for the service worker that will be generated
// It should be copied to the public directory of your Next.js app

export const serviceWorkerTemplate = `
import { createGraphQLHandler } from 'shared-utils/graphql-cache/workbox-strategies'

// Initialize the GraphQL handler
const graphqlHandler = createGraphQLHandler({
  strategy: 'stale-while-revalidate',
  maxAge: 3600, // 1 hour
  maxEntries: 100,
  networkTimeoutSeconds: 10,
  enabled: true,
  // Only cache queries, not mutations
  excludeOperations: ['CreateOrder', 'UpdateOrder', 'DeleteOrder'] // Add your mutation names here
})

// Initialize the handler when the service worker starts
self.addEventListener('install', (event) => {
  console.log('GraphQL Service Worker installing...')
  event.waitUntil(
    graphqlHandler.init().then(() => {
      console.log('GraphQL cache initialized')
      // Skip waiting to activate immediately
      return self.skipWaiting()
    })
  )
})

self.addEventListener('activate', (event) => {
  console.log('GraphQL Service Worker activating...')
  event.waitUntil(
    // Take control of all clients immediately
    self.clients.claim()
  )
})

// Handle fetch events
self.addEventListener('fetch', (event) => {
  // Only handle POST requests to GraphQL endpoints
  if (
    event.request.method === 'POST' && 
    (event.request.url.includes('/graphql') || event.request.url.includes('/api/graphql'))
  ) {
    event.respondWith(
      graphqlHandler.handleFetch(event).catch((error) => {
        console.error('GraphQL handler error:', error)
        // Fallback to normal fetch
        return fetch(event.request)
      })
    )
  }
  // Let other requests pass through normally
})

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  const { type, data } = event.data

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'GET_CACHE_STATS':
      // This would need to be implemented in the GraphQL handler
      event.ports[0].postMessage({ count: 0, size: 0 })
      break
      
    case 'CLEAR_CACHE':
      // This would need to be implemented in the GraphQL handler
      event.ports[0].postMessage({ success: true })
      break
      
    default:
      console.log('Unknown message type:', type)
  }
})

// Handle errors
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error)
})

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled rejection:', event.reason)
})
`

// Utility function to generate the service worker file content
export function generateServiceWorkerContent(config: {
  strategy?: 'stale-while-revalidate' | 'cache-first' | 'network-first'
  maxAge?: number
  maxEntries?: number
  networkTimeoutSeconds?: number
  excludeOperations?: string[]
  cacheableOperations?: string[]
} = {}): string {
  const {
    strategy = 'stale-while-revalidate',
    maxAge = 3600,
    maxEntries = 100,
    networkTimeoutSeconds = 10,
    excludeOperations = [],
    cacheableOperations = []
  } = config

  return \`// Auto-generated GraphQL Service Worker
// Do not edit this file directly

import { createGraphQLHandler } from 'shared-utils/graphql-cache/workbox-strategies'

const graphqlHandler = createGraphQLHandler({
  strategy: '\${strategy}',
  maxAge: \${maxAge},
  maxEntries: \${maxEntries},
  networkTimeoutSeconds: \${networkTimeoutSeconds},
  enabled: true,
  excludeOperations: \${JSON.stringify(excludeOperations)},
  cacheableOperations: \${JSON.stringify(cacheableOperations)}
})

self.addEventListener('install', (event) => {
  console.log('GraphQL Service Worker installing...')
  event.waitUntil(
    graphqlHandler.init().then(() => {
      console.log('GraphQL cache initialized')
      return self.skipWaiting()
    })
  )
})

self.addEventListener('activate', (event) => {
  console.log('GraphQL Service Worker activating...')
  event.waitUntil(self.clients.claim())
})

self.addEventListener('fetch', (event) => {
  if (
    event.request.method === 'POST' && 
    (event.request.url.includes('/graphql') || event.request.url.includes('/api/graphql'))
  ) {
    event.respondWith(
      graphqlHandler.handleFetch(event).catch((error) => {
        console.error('GraphQL handler error:', error)
        return fetch(event.request)
      })
    )
  }
})

self.addEventListener('message', (event) => {
  const { type } = event.data

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
    case 'GET_CACHE_STATS':
      event.ports[0].postMessage({ count: 0, size: 0 })
      break
    case 'CLEAR_CACHE':
      event.ports[0].postMessage({ success: true })
      break
  }
})

self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error)
})

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled rejection:', event.reason)
})
\`
}`
