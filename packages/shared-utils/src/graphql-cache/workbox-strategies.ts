import { GraphQLCacheManager } from "./GraphQLCacheManager"
import type { GraphQLRequest, WorkboxGraphQLConfig } from "./types"

// Service Worker types (only available in browser/service worker context)
declare global {
  interface ExtendableEvent extends Event {
    waitUntil(promise: Promise<any>): void
  }

  interface FetchEvent extends ExtendableEvent {
    request: Request
    respondWith(response: Promise<Response> | Response): void
  }
}

export class GraphQLWorkboxStrategy {
  private cacheManager: GraphQLCacheManager
  private config: WorkboxGraphQLConfig

  constructor(config: WorkboxGraphQLConfig = {}) {
    this.config = {
      strategy: "stale-while-revalidate",
      networkTimeoutSeconds: 10,
      ...config,
    }
    this.cacheManager = new GraphQLCacheManager(config)
  }

  async init(): Promise<void> {
    await this.cacheManager.init()
  }

  async handle(event: FetchEvent): Promise<Response> {
    const request = event.request.clone()

    try {
      const body = (await request.json()) as {
        query: string
        variables?: Record<string, any>
        operationName?: string
      }
      const graphqlRequest: GraphQLRequest = {
        query: body.query,
        variables: body.variables,
        operationName: body.operationName,
      }

      switch (this.config.strategy) {
        case "cache-first":
          return await this.cacheFirst(event, graphqlRequest)
        case "network-first":
          return await this.networkFirst(event, graphqlRequest)
        case "stale-while-revalidate":
        default:
          return await this.staleWhileRevalidate(event, graphqlRequest)
      }
    } catch (error) {
      console.error("Error handling GraphQL request:", error)
      return fetch(event.request)
    }
  }

  private async cacheFirst(
    event: FetchEvent,
    graphqlRequest: GraphQLRequest
  ): Promise<Response> {
    // Try cache first
    const cachedResponse = await this.cacheManager.get(graphqlRequest)
    if (cachedResponse) {
      return cachedResponse
    }

    // Fallback to network
    try {
      const networkResponse = await fetch(event.request.clone())
      if (networkResponse.ok) {
        await this.cacheManager.set(graphqlRequest, networkResponse.clone())
      }
      return networkResponse
    } catch (error) {
      console.error("Network request failed:", error)
      throw error
    }
  }

  private async networkFirst(
    event: FetchEvent,
    graphqlRequest: GraphQLRequest
  ): Promise<Response> {
    try {
      // Try network first with timeout
      const networkResponse = await this.fetchWithTimeout(event.request.clone())
      if (networkResponse.ok) {
        await this.cacheManager.set(graphqlRequest, networkResponse.clone())
      }
      return networkResponse
    } catch (error) {
      console.log("Network failed, trying cache:", error)

      // Fallback to cache
      const cachedResponse = await this.cacheManager.get(graphqlRequest)
      if (cachedResponse) {
        return cachedResponse
      }

      throw error
    }
  }

  private async staleWhileRevalidate(
    event: FetchEvent,
    graphqlRequest: GraphQLRequest
  ): Promise<Response> {
    const cachedResponse = await this.cacheManager.get(graphqlRequest)

    // Start network request in background
    const networkPromise = fetch(event.request.clone())
      .then(async (response) => {
        if (response.ok) {
          await this.cacheManager.set(graphqlRequest, response.clone())
        }
        return response
      })
      .catch((error) => {
        console.error("Background network request failed:", error)
        return null
      })

    // Return cached response immediately if available
    if (cachedResponse) {
      // Don't await the network request, let it run in background
      event.waitUntil(networkPromise)
      return cachedResponse
    }

    // If no cache, wait for network
    const networkResponse = await networkPromise
    if (networkResponse) {
      return networkResponse
    }

    throw new Error("Both cache and network failed")
  }

  private async fetchWithTimeout(request: Request): Promise<Response> {
    const timeoutMs = (this.config.networkTimeoutSeconds || 10) * 1000

    return Promise.race([
      fetch(request),
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("Network timeout")), timeoutMs)
      }),
    ])
  }
}

// Service Worker event handler
export function createGraphQLHandler(config: WorkboxGraphQLConfig = {}) {
  const strategy = new GraphQLWorkboxStrategy(config)

  return {
    async init() {
      await strategy.init()
    },

    async handleFetch(event: FetchEvent) {
      // Only handle POST requests to /graphql
      if (
        event.request.method === "POST" &&
        event.request.url.includes("/graphql")
      ) {
        return await strategy.handle(event)
      }

      // Let other requests pass through
      return fetch(event.request)
    },
  }
}
