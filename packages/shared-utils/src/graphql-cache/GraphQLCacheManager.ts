import { openDB, type DBSchema, type IDBPDatabase } from "idb"
import type {
  GraphQLRequest,
  GraphQLResponse,
  CachedGraphQLEntry,
  GraphQLCacheConfig,
} from "./types"

interface GraphQLCacheDB extends DBSchema {
  "graphql-cache": {
    key: string
    value: CachedGraphQLEntry
    indexes: {
      timestamp: number
    }
  }
}

export class GraphQLCacheManager {
  private db: IDBPDatabase<GraphQLCacheDB> | null = null
  private config: Required<GraphQLCacheConfig>

  constructor(config: GraphQLCacheConfig = {}) {
    this.config = {
      dbName: config.dbName || "GraphQL-Cache",
      storeName: config.storeName || "graphql-cache",
      maxAge: config.maxAge || 3600, // 1 hour
      maxEntries: config.maxEntries || 100,
      enabled: config.enabled !== false,
      cacheableOperations: config.cacheableOperations || [],
      excludeOperations: config.excludeOperations || [],
    }
  }

  async init(): Promise<void> {
    if (!this.config.enabled) return

    try {
      this.db = await openDB<GraphQLCacheDB>(this.config.dbName, 1, {
        upgrade(db) {
          if (!db.objectStoreNames.contains("graphql-cache")) {
            const store = db.createObjectStore("graphql-cache", {
              keyPath: "hash",
            })
            store.createIndex("timestamp", "timestamp")
          }
        },
      })
    } catch (error) {
      console.error("Failed to initialize GraphQL cache database:", error)
    }
  }

  private generateHash(request: GraphQLRequest): string {
    const normalizedRequest = {
      query: request.query.replace(/\s+/g, " ").trim(),
      variables: request.variables || {},
      operationName: request.operationName,
    }

    // Simple hash function - in production you might want to use crypto.subtle.digest
    const str = JSON.stringify(normalizedRequest)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  private shouldCache(request: GraphQLRequest): boolean {
    if (!this.config.enabled || !this.db) return false

    // Check if it's a query (not mutation/subscription)
    const trimmedQuery = request.query.trim()
    if (!trimmedQuery.toLowerCase().startsWith("query")) return false

    // Check exclude list
    if (
      request.operationName &&
      this.config.excludeOperations.includes(request.operationName)
    ) {
      return false
    }

    // Check include list (if specified)
    if (this.config.cacheableOperations.length > 0) {
      return request.operationName
        ? this.config.cacheableOperations.includes(request.operationName)
        : false
    }

    return true
  }

  async get(request: GraphQLRequest): Promise<Response | null> {
    if (!this.shouldCache(request)) return null

    try {
      const hash = this.generateHash(request)
      const entry = await this.db!.get("graphql-cache", hash)

      if (!entry) return null

      // Check if cache is expired
      const now = Date.now()
      if (now - entry.timestamp > this.config.maxAge * 1000) {
        await this.delete(hash)
        return null
      }

      console.log("GraphQL cache hit:", request.operationName || "anonymous")

      return new Response(JSON.stringify(entry.response.body), {
        status: entry.response.status,
        statusText: entry.response.statusText,
        headers: entry.response.headers,
      })
    } catch (error) {
      console.error("Error retrieving from GraphQL cache:", error)
      return null
    }
  }

  async set(request: GraphQLRequest, response: Response): Promise<void> {
    if (!this.shouldCache(request)) return

    try {
      const hash = this.generateHash(request)
      const responseClone = response.clone()

      // Serialize headers
      const headers: Record<string, string> = {}
      responseClone.headers.forEach((value, key) => {
        headers[key] = value
      })

      const body = (await responseClone.json()) as GraphQLResponse

      const entry: CachedGraphQLEntry = {
        query: request.query,
        variables: request.variables,
        operationName: request.operationName,
        response: {
          headers,
          status: responseClone.status,
          statusText: responseClone.statusText,
          body,
        },
        timestamp: Date.now(),
        hash,
      }

      await this.db!.put("graphql-cache", entry)

      // Clean up old entries if we exceed maxEntries
      await this.cleanup()

      console.log(
        "GraphQL response cached:",
        request.operationName || "anonymous"
      )
    } catch (error) {
      console.error("Error storing in GraphQL cache:", error)
    }
  }

  private async delete(hash: string): Promise<void> {
    try {
      await this.db!.delete("graphql-cache", hash)
    } catch (error) {
      console.error("Error deleting from GraphQL cache:", error)
    }
  }

  private async cleanup(): Promise<void> {
    try {
      const tx = this.db!.transaction("graphql-cache", "readonly")
      const count = await tx.store.count()

      if (count <= this.config.maxEntries) return

      // Get all entries sorted by timestamp (oldest first)
      const entries = await this.db!.getAllFromIndex(
        "graphql-cache",
        "timestamp"
      )
      const entriesToDelete = entries.slice(0, count - this.config.maxEntries)

      const deleteTx = this.db!.transaction("graphql-cache", "readwrite")
      for (const entry of entriesToDelete) {
        await deleteTx.store.delete(entry.hash)
      }
      await deleteTx.done
    } catch (error) {
      console.error("Error cleaning up GraphQL cache:", error)
    }
  }

  async clear(): Promise<void> {
    try {
      await this.db!.clear("graphql-cache")
      console.log("GraphQL cache cleared")
    } catch (error) {
      console.error("Error clearing GraphQL cache:", error)
    }
  }

  async getStats(): Promise<{ count: number; size: number }> {
    try {
      const entries = await this.db!.getAll("graphql-cache")
      const size = JSON.stringify(entries).length
      return { count: entries.length, size }
    } catch (error) {
      console.error("Error getting GraphQL cache stats:", error)
      return { count: 0, size: 0 }
    }
  }
}
