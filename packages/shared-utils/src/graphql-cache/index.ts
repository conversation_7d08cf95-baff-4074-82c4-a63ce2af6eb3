// GraphQL Cache utilities for Workbox integration
export { GraphQLCacheManager } from "./GraphQLCacheManager"
export {
  GraphQLWorkboxStrategy,
  createGraphQLHandler,
} from "./workbox-strategies"
export {
  GraphQLServiceWorkerManager,
  registerGraphQLServiceWorker,
  useGraphQLServiceWorker,
} from "./service-worker-registration"
// export { serviceWorkerTemplate, generateServiceWorkerContent } from './sw-template'
export type {
  GraphQLRequest,
  GraphQLResponse,
  CachedGraphQLEntry,
  GraphQLCacheConfig,
  ServiceWorkerConfig,
  CacheStrategy,
  WorkboxGraphQLConfig,
} from "./types"

// Convenience function to setup GraphQL caching in a Next.js app
export async function setupGraphQLCache(
  config: {
    serviceWorker?: {
      swUrl?: string
      scope?: string
    }
    cache?: {
      strategy?: "stale-while-revalidate" | "cache-first" | "network-first"
      maxAge?: number
      maxEntries?: number
      excludeOperations?: string[]
      cacheableOperations?: string[]
    }
  } = {}
) {
  const { serviceWorker = {}, cache = {} } = config

  // Register service worker
  const swManager = new GraphQLServiceWorkerManager({
    swUrl: serviceWorker.swUrl || "/graphql-sw.js",
    scope: serviceWorker.scope || "/",
  })

  try {
    const registration = await swManager.register()
    if (registration) {
      console.log("GraphQL caching enabled")
      return {
        registration,
        manager: swManager,
        clearCache: () => swManager.clearCache(),
        getCacheStats: () => swManager.getCacheStats(),
      }
    }
  } catch (error) {
    console.error("Failed to setup GraphQL cache:", error)
  }

  return null
}
