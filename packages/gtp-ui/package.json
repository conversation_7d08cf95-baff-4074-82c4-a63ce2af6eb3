{"name": "gtp-ui", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"codegen": "graphql-codegen --config codegen.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@graphql-typed-document-node/core": "^3.1.2", "@hookform/resolvers": "^2.9.11", "@react-google-maps/api": "^2.18.1", "@tanstack/react-query": "^4.24.4", "class-variance-authority": "^0.6.0", "dotenv": "*", "graphql": "^16.6.0", "graphql-request": "^5.2.0", "leaflet": "^1.8.0", "next": "*", "next-auth": "*", "react": "*", "react-feather": "^2.0.10", "react-google-autocomplete": "^2.7.3", "react-hook-form": "^7.43.1", "react-image-gallery": "^1.2.11", "react-leaflet": "^4.2.1", "react-query-kit": "^2.0.7", "shared-assets": "workspace:*", "shared-config": "workspace:*", "shared-ui": "workspace:*", "shared-utils": "workspace:*", "zod": "*", "zustand": "^4.3.3"}, "devDependencies": {"@graphql-codegen/cli": "^3.2.2", "@graphql-codegen/client-preset": "^2.1.1", "@graphql-typed-document-node/core": "^3.1.2", "@types/leaflet": "^1.9.0", "@types/lodash.debounce": "^4.0.7", "@types/react": "^18.0.26", "@types/react-image-gallery": "^1.2.0", "@typescript-eslint/eslint-plugin": "^5.47.1", "@typescript-eslint/parser": "^5.47.1", "eslint-config-next": "*", "typescript": "*"}}