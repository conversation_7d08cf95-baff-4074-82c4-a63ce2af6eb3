import { useState } from "react"
import { Plus } from "react-feather"
import { useFieldArray, useFormContext, useFormState } from "react-hook-form"
import { <PERSON><PERSON>, But<PERSON>, confirm, useSnackbar } from "shared-ui"
import { formatIntoCurrency } from "shared-utils"

import ProductFormModal from "./components/ProductFormModal/ProductFormModal"
import SelectedProductBox from "./components/SelectedProductBox"
import Fieldset from "@/components/fieldset"
import SectionContainer from "@/components/section-container"

import { calculateTotalPrice } from "./helpers/calculateTotalPrice"
import { Action } from "../../genericCompetitionSchema"
import {
  type TProductQualificationSchema,
  type TNonConstructionCompetitionSchema,
} from "../../non-construction/nonConstructionCompetitionSchema"
import ErrorField from "../ErrorField"

export default function ProductSection({
  isEdit,
}: Readonly<{ isEdit?: boolean }>) {
  const [isProductFormShown, setIsProductFormShown] = useState(false)
  const { control, trigger, watch } =
    useFormContext<TNonConstructionCompetitionSchema>()
  const { append, fields, remove, update } = useFieldArray({
    name: "productQualifications.products",
    control,
  })
  const {
    errors: { productQualifications },
  } = useFormState({
    control,
    name: "productQualifications.products",
  })

  const products = watch("productQualifications.products")
  const bannerErrorMessage = productQualifications?.products?.message
  const captionErrorMessage =
    bannerErrorMessage === "Detail Produk harus diisi"
      ? bannerErrorMessage
      : undefined

  // Total price mechanism
  const totalPrice = calculateTotalPrice(fields)

  //  Edit mechanism
  const [currentEditedItemIdx, setCurrentEditedItemIdx] = useState<
    number | null
  >(null)

  function editClickHandler(
    itemIdx: number,
    productQualificationId?: string | null
  ) {
    if (isEdit) {
      const editedDataIdx = fields.findIndex(
        (field) => field.productQualificationId === productQualificationId
      )
      setCurrentEditedItemIdx(editedDataIdx)
    } else {
      setCurrentEditedItemIdx(itemIdx)
    }
    setIsProductFormShown(true)
  }

  async function deleteClickHandler(
    itemIdx: number,
    productQualificationId?: string | null
  ) {
    const confirmed = await confirm({
      title: `Yakin Ingin Menghapus Kategori?`,
      description: `Setelah Anda klik "Hapus Kategori", seluruh data spesifikasi produk yang sudah dimasukkan akan terhapus.`,
      confirmText: "Hapus Kategori",
      cancelText: "Batal",
    })
    if (confirmed) {
      if (isEdit) {
        const editedDataIdx = fields.findIndex(
          (field) => field.productQualificationId === productQualificationId
        )
        const product = fields?.[editedDataIdx]
        if (product) {
          const tmpData: TProductQualificationSchema = {
            ...product,
            action: Action.DELETE,
          }
          update(editedDataIdx, tmpData)
        }
        return
      }
      remove(itemIdx)
    }
  }

  // Form Submission Mechanism
  const { enqueueSnackbar } = useSnackbar()
  function handleFormSubmission(productData: TProductQualificationSchema) {
    setIsProductFormShown((currVal) => !currVal)

    if (currentEditedItemIdx !== null && currentEditedItemIdx !== undefined) {
      if (isEdit) {
        const tmpData: TProductQualificationSchema = {
          ...productData,
          action: Action.UPDATE,
        }
        update(currentEditedItemIdx, tmpData)
      } else {
        update(currentEditedItemIdx, productData)
      }
      setCurrentEditedItemIdx(null)
      trigger("productQualifications")
      return
    }

    append(productData)
    enqueueSnackbar({
      message: "Detail Produk berhasil ditambahkan.",
      type: "success",
    })
    trigger("productQualifications")
  }

  const ModalOnClose = () => {
    setIsProductFormShown(false)
    setCurrentEditedItemIdx(null)
  }

  return (
    <SectionContainer>
      <Fieldset
        name="Detail Produk"
        description="Tambahkan satu atau lebih Detail Produk sesuai kebutuhan kompetisi."
        id="product-section"
      >
        {isProductFormShown && (
          <ProductFormModal
            onClose={ModalOnClose}
            onSubmit={handleFormSubmission}
            product={products?.[currentEditedItemIdx as number]}
          />
        )}
        {!isEdit && (
          <div className="flex w-[100%] justify-end">
            <div className="flex flex-col items-start">
              <Button
                id="add-product-qualification"
                type="button"
                className="w-[18vw] bg-tertiary25 hover:bg-secondary50"
                variant="soft"
                IconLeft={Plus}
                onClick={() => setIsProductFormShown(true)}
              >
                <span className="text-caption-lg-semibold">
                  Tambah Detail Produk
                </span>
              </Button>
              {!!captionErrorMessage && (
                <ErrorField errorMessage={captionErrorMessage} />
              )}
            </div>
          </div>
        )}
      </Fieldset>

      {fields.length > 0 && (
        <div className="flex flex-col gap-2 px-6 pt-6">
          {bannerErrorMessage && (
            <Alert
              id="error"
              className="break-anywhere"
              variant="error"
              description={bannerErrorMessage}
            />
          )}
          <div>
            <div className="text-caption-lg-regular text-tertiary300">
              Total Pagu Kompetisi{" "}
              <span className="text-caption-lg-semibold text-tertiary500">
                {formatIntoCurrency({ value: totalPrice, withSpacing: false })}
              </span>
            </div>
          </div>
        </div>
      )}

      <SelectedProductBox
        onDeleteClicked={deleteClickHandler}
        selectedCategories={fields}
        onEditClicked={editClickHandler}
        isEdit={isEdit}
      />
    </SectionContainer>
  )
}
