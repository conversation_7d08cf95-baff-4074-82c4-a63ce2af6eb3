import { useState } from "react"
import { useFieldArray, useFormContext, useFormState } from "react-hook-form"
import { <PERSON><PERSON>, But<PERSON>, confirm, useSnackbar } from "shared-ui"
import { formatIntoCurrency } from "shared-utils"

import ProductFormModalConstruction from "./components/ProductFormModalConstruction/ProductFormModalConstruction"
import SelectedProductBoxConstruction from "./components/SelectedProductBoxConstruction"
import Fieldset from "@/components/fieldset"
import SectionContainer from "@/components/section-container"

import { calculateTotalPrice } from "./helpers/calculateTotalPrice"
import {
  type TProductQualificationSchemaConstruction,
  type TConstructionCompetitionSchema,
} from "../../construction/constructionCompetitionSchema"
import { Action } from "../../genericCompetitionSchema"
import ErrorField from "../ErrorField"

export default function ProductSectionConstruction({
  isEdit,
}: Readonly<{ isEdit?: boolean }>) {
  const [isProductFormShown, setIsProductFormShown] = useState(false)
  const { control, trigger, watch } =
    useFormContext<TConstructionCompetitionSchema>()
  const { append, fields, remove, update } = useFieldArray({
    name: "productQualifications.products",
    control,
  })
  const {
    errors: { productQualifications },
  } = useFormState({
    control,
    name: "productQualifications.products",
  })
  const products = watch("productQualifications.products")
  const bannerErrorMessage = productQualifications?.products?.message
  const captionErrorMessage =
    bannerErrorMessage === "Anda belum menambahkan detail pekerjaan konstruksi."
      ? bannerErrorMessage
      : undefined

  // Total price mechanism
  const totalPrice = calculateTotalPrice(fields)

  //  Edit mechanism
  const [currentEditedItemIdx, setCurrentEditedItemIdx] = useState<
    number | null
  >(null)

  function editClickHandler(
    itemIdx: number,
    productQualificationId?: string | null
  ) {
    if (isEdit) {
      const editedDataIdx = fields.findIndex(
        (field) => field.productQualificationId === productQualificationId
      )
      setCurrentEditedItemIdx(editedDataIdx)
    } else {
      setCurrentEditedItemIdx(itemIdx)
    }
    setIsProductFormShown(true)
  }

  async function deleteClickHandler(
    itemIdx: number,
    productQualificationId?: string | null
  ) {
    const confirmed = await confirm({
      title: `Yakin Ingin Menghapus Kategori?`,
      description: `Setelah Anda klik "Hapus Kategori", seluruh data spesifikasi produk yang sudah dimasukkan akan terhapus.`,
      confirmText: "Hapus Kategori",
      cancelText: "Batal",
    })
    if (confirmed) {
      if (isEdit) {
        const editedDataIdx = fields.findIndex(
          (field) => field.productQualificationId === productQualificationId
        )
        const product = fields?.[editedDataIdx]
        if (product) {
          const tmpData: TProductQualificationSchemaConstruction = {
            ...product,
            action: Action.DELETE,
          }
          update(editedDataIdx, tmpData)
        }
        return
      }
      remove(itemIdx)
    }
  }

  // Form Submission Mechanism
  const { enqueueSnackbar } = useSnackbar()
  function handleFormSubmission(
    productData: TProductQualificationSchemaConstruction
  ) {
    setIsProductFormShown((currVal) => !currVal)
    if (currentEditedItemIdx !== null && currentEditedItemIdx !== undefined) {
      if (isEdit) {
        const tmpData: TProductQualificationSchemaConstruction = {
          ...productData,
          action: Action.UPDATE,
        }
        update(currentEditedItemIdx, tmpData)
      } else {
        update(currentEditedItemIdx, productData)
      }
      setCurrentEditedItemIdx(null)
      trigger("productQualifications")
      return
    }
    append(productData)
    enqueueSnackbar({
      message: "Detail Produk berhasil ditambahkan.",
      type: "success",
    })
    trigger("productQualifications")
  }
  const ModalOnClose = () => {
    setIsProductFormShown(false)
    setCurrentEditedItemIdx(null)
  }

  return (
    <SectionContainer>
      <Fieldset
        name="Detail Pekerjaan Konstruksi"
        description="Tambahkan satu atau lebih detail pekerjaan sesuai kebutuhan kompetisi."
        id="construction-product-section"
      >
        {isProductFormShown && (
          <ProductFormModalConstruction
            onClose={ModalOnClose}
            onSubmit={handleFormSubmission}
            product={products?.[currentEditedItemIdx as number]}
          />
        )}
        {!isEdit && (
          <div className="flex w-[100%] justify-end">
            <div className="flex flex-col items-start">
              <Button
                id="add-construction-product-qualification"
                type="button"
                className="w-[330px] bg-tertiary25 hover:bg-secondary50"
                variant="soft"
                onClick={() => setIsProductFormShown(true)}
              >
                <span className="text-caption-lg-semibold">
                  Tambah Kualifikasi Pekerjaan
                </span>
              </Button>
              {!!captionErrorMessage && (
                <ErrorField errorMessage={captionErrorMessage} />
              )}
            </div>
          </div>
        )}
      </Fieldset>

      {fields.length > 0 && (
        <div className="flex flex-col gap-2 px-6 pt-6">
          {bannerErrorMessage && (
            <Alert
              id="error"
              className="break-anywhere"
              variant="error"
              description={bannerErrorMessage}
            />
          )}
          <div>
            <div className="text-caption-lg-regular text-tertiary300">
              Total Pagu Kompetisi{" "}
              <span className="text-caption-lg-semibold text-tertiary500">
                {formatIntoCurrency({ value: totalPrice, withSpacing: false })}
              </span>
            </div>
          </div>
        </div>
      )}

      <SelectedProductBoxConstruction
        onDeleteClicked={deleteClickHandler}
        selectedCategories={fields}
        onEditClicked={editClickHandler}
        isEdit={isEdit}
      />
    </SectionContainer>
  )
}
