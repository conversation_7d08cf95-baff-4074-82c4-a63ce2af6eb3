import { type TypeOf, z } from "zod"

import {
  titleSchema,
  arrivalRequestDateSchema,
  rupSchema,
  reviewerPPKSchema,
  type TCompetitionRupSchema,
  vendorQualificationSchema,
  addressSchema,
  Action,
  validateTransactionLimits,
  validateCompetitionForm,
} from "../genericCompetitionSchema"
import { validateSchedule } from "../utils/validateSchedule"
import {
  FormType,
  IntegrationProvider,
  IntegrationSource,
} from "@/federatedGql/graphql"

const competitionScheduleSchema = (isEdit?: boolean) =>
  z
    .object({
      date: z
        .object({
          to: z.date().optional(),
          from: z.date().optional(),
        })
        .optional(),
      time: z
        .object({
          to: z.string().optional(),
          from: z.string().optional(),
        })
        .optional(),
      additionalData: z
        .object({
          endPeriodLimitSeconds: z.number().optional(),
          cutOffDate: z.string().optional(),
        })
        .optional(),
    })
    .optional()
    .superRefine((data, ctx) => {
      validateSchedule({
        date: { from: data?.date?.from, to: data?.date?.to },
        time: {
          from: data?.time?.from,
          to: data?.time?.to,
        },
        ctx,
        isEdit,
        additionalData: data?.additionalData,
      })
    })

export type TCompetitionSchedule = z.infer<
  ReturnType<typeof competitionScheduleSchema>
>

export function validateCompetitionValueByRup(
  selectedRup: TCompetitionRupSchema,
  totalCompetitionValue: number,
  ctx: z.RefinementCtx,
  isEdit?: boolean,
  totalPenawar?: number | null
) {
  if (!selectedRup) return
  if (totalCompetitionValue > selectedRup?.totalBalance) {
    const editHasPenawar = isEdit && totalPenawar && totalPenawar > 0
    ctx.addIssue({
      message: "Nominal tidak boleh melebihi pagu RUP",
      code: "custom",
      path: editHasPenawar ? ["rup"] : ["productQualifications.products"],
    })
  }
}

export type TSchedule = {
  competitionSchedule: {
    date?: {
      from?: Date
      to?: Date
    }
    time?: {
      from?: Date
      to?: Date
    }
  }
}

export const categoryAttribute = z.object({
  id: z.string(),
  name: z.string(),
  value: z.string(),
  type: z.nativeEnum(FormType),
  defaultValue: z.string(),
})

export const categoryAttributeSchema = z.object({
  attributes: z.array(categoryAttribute),
  pricing: z.object({
    // NOTES: Might need to get the max value from each category's master product
    estimatedPrice: z
      .number()
      .max(999999999999, {
        message: "Harga tidak boleh lebih dari Rp999.999.999.999",
      })
      .nullable(),
    qty: z.number(),
    unit: z.object({ value: z.string(), label: z.string() }).nullable(),
  }),
})

export type TCategoryAttributeSchema = TypeOf<typeof categoryAttributeSchema>

export const category = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  productType: z.string(),
  kbli: z.array(z.string()),
  detailedSpec: categoryAttributeSchema,
  productQualificationId: z.string().nullish(),
  action: z.nativeEnum(Action).optional(),
  integrationSource: z.nativeEnum(IntegrationSource).optional().nullish(),
  integrationProvider: z.nativeEnum(IntegrationProvider).optional().nullish(),
  orderId: z.string().optional().nullish(),
  orderKey: z.string().optional().nullish(),
  hasReviewerApproved: z.boolean().optional(),
})

export type TSelectedCategory = TypeOf<typeof category>

export const productQualificationSchema = category

export type TProductQualificationSchema = TypeOf<
  typeof productQualificationSchema
>

export const productQualificationsSchema = (
  validationMessage: string,
  isCompetitionAbove1MEnabled?: boolean
) =>
  z
    .object({
      products: z
        .array(productQualificationSchema)
        .default([])
        .refine((val) => val.length > 0, validationMessage),
      additionalData: z
        .object({
          role: z.string().optional(),
          PPKCheckoutThreshold: z.number().optional(),
          PPCheckoutLimit: z.number().optional(),
        })
        .optional(),
    })
    .superRefine((prodQualifications, ctx) => {
      validateTransactionLimits(prodQualifications, ctx)

      const totalCompetitionValue = prodQualifications.products.reduce(
        (sum, currCategory) =>
          (currCategory.detailedSpec.pricing.estimatedPrice ?? 0) *
            currCategory.detailedSpec.pricing.qty +
          sum,
        0
      )
      if (!isCompetitionAbove1MEnabled && totalCompetitionValue > 1000000000) {
        ctx.addIssue({
          path: ["products"],
          message:
            "Mohon maaf, pembuatan kompetisi diatas Rp 1 miliar untuk sementara tidak tersedia dikarenakan sedang masa perbaikan.",
          code: "custom",
        })
      }
    })

export type TProductSchema = z.infer<
  ReturnType<typeof productQualificationsSchema>
>
const nonConstructionCompetitionSchema = (
  defaultValue: TSchedule,
  isEdit?: boolean,
  isCompetitionAbove1MEnabled?: boolean,
  isKlpd?: boolean
) =>
  z
    .object({
      rup: rupSchema(isKlpd),
      title: titleSchema,
      competitionSchedule: competitionScheduleSchema(isEdit),
      vendorQualifications: vendorQualificationSchema,
      productQualifications: productQualificationsSchema(
        "Detail Produk harus diisi",
        isCompetitionAbove1MEnabled
      ),
      isItemized: z.boolean(),
      totalPenawar: z.number().nullish(),
      shippingAddress: addressSchema("Alamat Pengiriman harus dipilih"),
      arrivalRequestDate: arrivalRequestDateSchema,
      reviewer: reviewerPPKSchema,
    })
    .superRefine(
      (
        {
          competitionSchedule,
          arrivalRequestDate,
          productQualifications,
          rup,
          totalPenawar,
          reviewer,
        },
        ctx
      ) => {
        validateCompetitionForm(
          {
            competitionSchedule,
            arrivalRequestDate,
            productQualifications,
            rup,
            totalPenawar,
            reviewer,
          },
          defaultValue,
          ctx,
          isEdit,
          isKlpd
        )
      }
    )

export type TNonConstructionCompetitionSchema = z.infer<
  ReturnType<typeof nonConstructionCompetitionSchema>
>

export default nonConstructionCompetitionSchema
