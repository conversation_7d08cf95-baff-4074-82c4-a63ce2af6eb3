import { graphql } from "@/federatedGql/gql"

export const PRODUCT_SNAPSHOT = graphql(`
  query productSnapshot($input: [BulkProductSnapshotInput!]) {
    bulkGetProductSnapshots(input: $input) {
      ... on ProductSnapshots {
        __typename
        items {
          id
          category {
            curationEnabled
          }
          brand {
            brandName
            url
            status
          }
          actionReasonsActor {
            klpdDescription
          }
          images {
            imageUrl
          }
          videoUrl
          isActive
          isSellerUMKK
          kbki
          labels
          name
          pdn {
            type
            countryCode
            countryName
          }
          productAddOns {
            createdAt
            description
            id
            name
            productAddOnVariants {
              createdAt
              description
              id
              name
              price
            }
            type
            tax {
              ppnPercentage
            }
          }
          createdAt
          description
          productInformations {
            additionalInformations {
              customFormFieldId
              createdAt
              id
              name
              value
            }
            documents {
              customFormFieldId
              id
              name
              value
            }
            mainInformations {
              createdAt
              id
              name
              value
            }
          }
          sellerId
          sellerLocation {
            cityName
          }
          sellerName
          sellerVillageAreaCode
          sellerInfo {
            shippingAddresses {
              villageAreaCode
              regionDetail {
                cityName
              }
            }
          }
          shipping {
            dimensionUnit
            height
            length
            weight
            weightUnit
            width
          }
          slug
          stockUnit {
            primaryUnit
          }
          tkdn {
            number
            value
            bmpValue
            type
            status
            companyName
            url
          }
          sni {
            sniNumber
            url
            companyName
            status
          }
          type
          username
          variants {
            createdAt
            id
            imageUrl
            optionValues
            options
            price
            sku
          }
          tax {
            ppnBmTaxPaymentFileToken
            ppnBmTaxPaymentStatus
          }
          version
        }
      }
      ... on GenericError {
        __typename
        code
        message
        reqId
      }
    }
  }
`)

export const MINICOM_SNAPSHOT_DATA = graphql(`
  query minicomSnapshotData($input: ProposalProductDataInput!) {
    minikomProposalProductData(input: $input) {
      ... on ProposalProductDataResponse {
        __typename
        proposals {
          proposalId
          proposalProducts {
            addOnVariantIds
            productId
            productVariantId
            proposalDetailId
          }
          proposedAt
        }
      }
      ... on GenericError {
        __typename
        code
        message
        reqId
      }
    }
  }
`)

export const ORDER_SNAPSHOT_DATA = graphql(`
  query orderSnapshotData($input: OrderInput!) {
    orderDetail(input: $input) {
      ... on Order {
        __typename
        timestamp
        isFtz
        items {
          lastPrice
          productId
          productVariantId
          addOns {
            addOnVariantId
            lastPrice
            id
            tax {
              ppn
              ppnBm
              ppnBmPercentage
              ppnPercentage
            }
          }
          tax {
            ppn
            ppnBm
            ppnBmPercentage
            ppnPercentage
          }
        }
      }
      ... on GenericError {
        __typename
        reqId
        code
        message
      }
    }
  }
`)
