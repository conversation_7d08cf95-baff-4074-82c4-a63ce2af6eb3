import { type TSnapshotProduct } from "gtp-ui/src/snapshot-product/type"
import { formatDate } from "shared-utils"

import { type TGeneralData } from "./generateGeneralData"
import generatePriceVariantSnapshotData from "./generatePriceVariantSnapshotData"
import { type TProductSlugResponse } from "../serverRequests/productSlugServerQuery"
import { type TProductSnapshotResponse } from "../serverRequests/productSnapshotServerQuery"

export default function generateSnapshotData({
  productData,
  generalData,
  source,
  productSlug,
}: {
  productData: NonNullable<TProductSnapshotResponse["items"]>[0]
  generalData: TGeneralData
  source: string
  productSlug: TProductSlugResponse
}): TSnapshotProduct {
  const { images, videoUrl } = productData || {}
  const imgSlider = (images || []).reduce((acc: string[], img) => {
    if (img.imageUrl) {
      acc.push(img.imageUrl)
    }
    return acc
  }, [])

  if (videoUrl) {
    imgSlider.push(videoUrl)
  }

  const priceVariantInformation = generatePriceVariantSnapshotData({
    productData,
    generalData,
    source,
  })

  return {
    images: imgSlider,
    productInformation: {
      name: productData?.name,
      labels: productData?.labels,
      slug: productSlug?.slug,
      isFtz: generalData.isFtz,
    },
    priceVariantInformation,
    pdnInformation: {
      tkdn: {
        value: productData?.tkdn?.value,
        bmpValue: productData?.tkdn?.bmpValue,
        status: productData?.tkdn?.status,
        companyName: productData?.tkdn?.companyName,
        type: productData?.tkdn?.type,
        url: productData?.tkdn?.url,
        number: productData?.tkdn?.number,
      },
      pdn: {
        type: productData?.pdn?.type,
        countryName: productData?.pdn?.countryName,
        countryCode: productData?.pdn?.countryCode,
      },
    },
    detailInformation: {
      description: productData?.description,
      category: {
        curationEnabled: productData?.category?.curationEnabled,
      },
      productType: productData?.type,
      shipping: {
        weight: productData?.shipping?.weight,
        weightUnit: productData?.shipping?.weightUnit,
        width: productData?.shipping?.width,
        height: productData?.shipping?.height,
        length: productData?.shipping?.length,
        dimensionUnit: productData?.shipping?.dimensionUnit,
      },
      primaryUnit: productData?.stockUnit?.primaryUnit,
      mainInformations: productData?.productInformations.mainInformations,
      additionalInformations:
        productData?.productInformations.additionalInformations,
      brand: {
        brandName: productData?.brand?.brandName,
        url: productData?.brand?.url,
        status: productData?.brand?.status,
      },
      sni: {
        companyName: productData?.sni?.companyName,
        sniNumber: productData?.sni?.sniNumber,
        url: productData?.sni?.url,
        status: productData?.sni?.status,
      },
      klpdDescription: productData?.actionReasonsActor?.klpdDescription,
      kbki: productData?.kbki,
      sku: priceVariantInformation.selectedVariantSKU,
      documents: productData?.productInformations.documents?.map(
        (document) => ({
          customFormFieldId: document.customFormFieldId,
          id: document.id,
          name: document.name,
          value: document.value,
        })
      ),
      tax: {
        ppnBmTaxPaymentFileToken: productData?.tax?.ppnBmTaxPaymentFileToken,
        ppnBmTaxPaymentStatus: productData?.tax?.ppnBmTaxPaymentStatus,
      },
    },
    date: {
      dateLabel: formatDate(generalData.date, "dd MMM yyyy"),
      timeLabel: formatDate(generalData.date, "HH:mm"),
    },
    sellerInformation: {
      name: productData?.sellerName,
      isUmkk: productData?.isSellerUMKK,
      username: productData?.username,
      city: productData?.sellerLocation?.cityName ?? "-",
    },
  }
}
