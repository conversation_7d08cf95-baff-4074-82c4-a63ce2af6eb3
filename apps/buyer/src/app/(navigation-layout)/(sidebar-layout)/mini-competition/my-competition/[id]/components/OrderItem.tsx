import { ChevronRight } from "react-feather"

type TGroupedOrder = {
  orderId: string
  orderKey: string
  names: string[]
  hasReviewerApproved?: boolean
}

type OrderItemProps = {
  order: TGroupedOrder
  onClick?: () => void
}

const OrderItem = ({ order, onClick }: OrderItemProps) => {
  const isDisabled = !order.hasReviewerApproved

  const content = (
    <div className="text-caption-sm-regular w-11/12 overflow-hidden whitespace-nowrap text-tertiary500">
      <div><PERSON><PERSON></div>
      <div className="text-caption-lg-semibold">{order.orderId}</div>
      <div className="overflow-hidden text-ellipsis text-tertiary300">
        {order.names.join(", ")}
      </div>
    </div>
  )

  return isDisabled ? (
    <div
      className="flex cursor-not-allowed items-center gap-x-2 rounded-4 border border-tertiary50 p-3 text-left opacity-50"
      key={order.orderId}
    >
      {content}
      <ChevronRight />
    </div>
  ) : (
    <button
      type="button"
      className="flex items-center gap-x-2 rounded-4 border border-tertiary50 p-3 text-left"
      onClick={onClick}
      key={order.orderId}
    >
      {content}
      <ChevronRight />
    </button>
  )
}

export type { TGroupedOrder }
export default OrderItem
