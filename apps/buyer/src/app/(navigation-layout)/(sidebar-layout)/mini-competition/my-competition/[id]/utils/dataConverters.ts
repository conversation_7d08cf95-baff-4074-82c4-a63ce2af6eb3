import { toTitleCase } from "shared-utils"

import { COMPETITION_LOG_KEY_MAPPINGS } from "../constants"
import { type TCompetitionDetailServerQueryRes } from "../serverRequests/minicomCompetitionDetailServerQuery"
import { type TCommitmentOfficer, type TMiniCompetitionLogs } from "../type"
import { type TSelectedAddress } from "@/app/(navigation-layout)/mini-competition/create/genericCompetitionSchema"
import { type TSelectedCategory } from "@/app/(navigation-layout)/mini-competition/create/non-construction/nonConstructionCompetitionSchema"
import { formatTime } from "@/app/(navigation-layout)/mini-competition/create/utils/formatTime"
import {
  type CategoryType,
  type FormType,
  CompetitionLogKey,
} from "@/federatedGql/graphql"

export const constructScheduleData = (
  schedule: TCompetitionDetailServerQueryRes["schedule"]
) => {
  if (!schedule) {
    return {
      date: undefined,
    }
  }
  const startPeriod = schedule?.startPeriod
    ? new Date(schedule?.startPeriod as string)
    : undefined
  const endPeriod = schedule?.endPeriod
    ? new Date(schedule?.endPeriod as string)
    : undefined

  const startTime = formatTime(startPeriod)
  const endTime = formatTime(endPeriod)

  return {
    date: {
      from: startPeriod,
      to: endPeriod,
    },
    time: {
      from: startTime,
      to: endTime,
    },
  }
}

export const constructShipmentData = (
  shipment: TCompetitionDetailServerQueryRes["shipments"]
): TSelectedAddress => {
  if (!shipment?.[0]) return undefined
  const destination = shipment[0].destination

  return {
    id: destination.id ?? "",
    snapshotId: destination.snapshotId ?? "",
    shipmentId: shipment[0].id,
    phone: destination.phoneNumber ?? "",
    region: {
      province: destination.regionDetail.provinceName ?? "",
      city: destination.regionDetail.cityName ?? "",
      district: destination.regionDetail.districtName ?? "",
      village: destination.regionDetail.villageName ?? "",
    },
    latLng: {
      lat: destination.latitude ?? 0,
      lng: destination.longitude ?? 0,
    },
    label: destination.label ?? "",
    notes: destination.notes ?? "",
    villageAreaCode: destination.villageAreaCode ?? "",
    postalCode: destination.postalCode ?? "",
    receiverName: destination.receiverName ?? "",
    isMainAddress: Boolean(destination.isMainAddress),
    fullAddress: destination.fullAddress ?? "",
  }
}

export const constructProductData = (
  products: TCompetitionDetailServerQueryRes["items"]
): (TSelectedCategory & {
  sectoralProduct?: TCompetitionDetailServerQueryRes["items"][number]["sectoralProduct"]
  jobName?: TCompetitionDetailServerQueryRes["items"][number]["jobName"]
  hasReviewerApproved?: TCompetitionDetailServerQueryRes["items"][number]["hasReviewerApproved"]
})[] => {
  return products.map((product) => ({
    id: product.category.id,
    orderId: product.orderId,
    orderKey: product.orderKey,
    productQualificationId: product.id,
    name: `${product.category.parent?.parent?.name} > ${product.category?.parent?.name} > ${product.category.name}`,
    type: product.category.type as CategoryType,
    productType: product.category.productType as string,
    kbli: product.category.kbli ?? [],
    detailedSpec: {
      attributes: product.specs.map((spec) => ({
        ...spec,
        value: spec.value ?? "",
        defaultValue: spec.defaultValue ?? "",
        type: spec.type as FormType,
      })),
      pricing: {
        estimatedPrice: product.price ?? 0,
        qty: product.qty ?? 0,
        unit: {
          value: product.unit,
          label: toTitleCase(product.unit.split("_").join(" ")),
        },
      },
    },
    sectoralProduct: product.sectoralProduct,
    jobName: product.jobName,
    hasReviewerApproved: product.hasReviewerApproved,
  }))
}

export const constructVendorQualificationData = (
  vendorQualification: TCompetitionDetailServerQueryRes["sellerQualification"]
) => {
  if (vendorQualification.skalaUsaha.length < 1) return []

  const grouped: Record<string, string[]> =
    vendorQualification.skalaUsaha.reduce((newObj, item) => {
      const company = item.company.replace("_", "-")
      if (!newObj[company]) {
        newObj[company] = []
      }
      newObj[company]?.push(toTitleCase(item.oss))

      return newObj
    }, {} as Record<string, string[]>)

  return Object.entries(grouped).map(([key, values]) => {
    return `${key} - ${values.join(", ")}`
  })
}

type TValueShipment = {
  id: string
  destinationId: string
  destinationSnapshotId: string
  city: string
  province: null
  requestArrivalDate: Date
  isDeleted: boolean
}

export const constructCompetitionLogData = (
  logs: TCompetitionDetailServerQueryRes["logs"]
) => {
  if (logs.length < 1) return []

  return logs
    .reduce((arr: TMiniCompetitionLogs[], log) => {
      if (log.activity !== "CREATE") {
        const changeFields = log.after.reduce(
          (newArr: (string | undefined)[], item) => {
            if (
              item.key !== CompetitionLogKey.Reason &&
              item.key !== CompetitionLogKey.Total
            ) {
              if (item.key === CompetitionLogKey.Shipment) {
                const shipment = (JSON.parse(item.value) as TValueShipment[])[0]
                const destination = COMPETITION_LOG_KEY_MAPPINGS["DESTINATION"]
                const reqArrivalDate =
                  COMPETITION_LOG_KEY_MAPPINGS["REQUEST_ARRIVAL_DATE"]

                newArr.push(
                  ...(shipment?.destinationId !== null ? [destination] : []),
                  ...(shipment?.requestArrivalDate !== null
                    ? [reqArrivalDate]
                    : [])
                )
              } else {
                newArr.push(COMPETITION_LOG_KEY_MAPPINGS[item.key])
              }
            }
            return newArr
          },
          []
        )

        const isStatus =
          changeFields.length === 1 &&
          changeFields.includes(
            COMPETITION_LOG_KEY_MAPPINGS[CompetitionLogKey.Status]
          )

        arr.push({
          id: log.id,
          date: log.timestamp as string,
          username: log.actor.isSystem
            ? "Sistem"
            : log.actor.userInfo.name ?? "",
          changeFields,
          ...(isStatus && {
            valueBefore: log?.before?.[0]?.value,
            valueAfter: log?.after?.[0]?.value,
          }),
        })
      }
      return arr
    }, [])
    .filter((log) => log.changeFields.length > 0)
}

export const constructReviewerData = (
  data: TCompetitionDetailServerQueryRes["buyerInfo"]
): TCommitmentOfficer | undefined => {
  const reviewerData = data?.reviewer
  if (!reviewerData?.userInfo?.id) return undefined

  const satkerData = reviewerData?.personaDetail.satker
  return {
    id: reviewerData?.userInfo.id ?? "",
    instituteName: reviewerData?.personaDetail.satker?.namaKlpd ?? "",
    name: reviewerData?.userInfo.name ?? "",
    workUnitName: satkerData
      ? `${satkerData.kodeSatker} - ${satkerData.namaSatker}`
      : "",
    unitKerja: reviewerData?.personaDetail.persona?.unit ?? "",
  }
}
