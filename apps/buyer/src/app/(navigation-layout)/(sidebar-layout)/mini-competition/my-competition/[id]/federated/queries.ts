import { graphql } from "@/federatedGql/gql"

export const MINICOM_COMPETITION_DETAIL_QUERY = graphql(`
  query minikomCompetitionDetail(
    $input: CompetitionDetailInput!
    $isSipastiEnabled: Boolean!
  ) {
    minikomCompetitionDetail(input: $input) {
      ... on Competition {
        __typename
        id
        key
        title
        totalProposal
        competitionNumber
        publishedAt
        status
        total
        totalProposal
        reason
        isKonstruksi
        proposals {
          status
          documents {
            name
            size
            type
            url
          }
        }
        payment {
          description
          type
        }
        documents {
          name
          size
          token
          type
          url
        }
        sellerQualification {
          skalaUsaha {
            company
            oss
          }
          isItemized
        }
        buyerInfo {
          user {
            personaDetail {
              satker {
                namaKlpd
              }
            }
          }
          reviewer {
            userInfo {
              id
              name
              username
            }
            personaDetail {
              personaId
              persona {
                institusiId
                userId
                appUserName
                unit
              }
              satker {
                namaKlpd
                namaSatker
                kodeSatker
              }
            }
          }
        }
        schedule {
          endPeriod
          startPeriod
        }
        shipments {
          id
          destination {
            id
            receiverName
            label
            phoneNumber
            fullAddress
            regionDetail {
              cityName
              districtName
              provinceName
              villageName
            }
            notes
            snapshotId
            latitude
            longitude
            postalCode
            villageAreaCode
            isMainAddress
          }
          requestArrivalDate
        }
        items {
          hasReviewerApproved
          sectoralProduct @include(if: $isSipastiEnabled) {
            id
            name
            referenceType
            jobType
            materials {
              id
              name
              coefficient
            }
            tools {
              id
              name
              coefficient
            }
            workerPackages {
              id
              name
              coefficient
            }
          }
          jobName
          category {
            id
            name
            type
            productType
            kbli
            integrationProvider
            integrationSource
            isActive
            parent {
              name
              parent {
                name
              }
            }
          }
          id
          price
          qty
          specs {
            defaultValue
            id
            name
            type
            value
          }
          unit
          orderId
          orderKey
        }
        rup {
          id
          rupCode
          name
          totalBalance
          fundingSourceDetails {
            mak
          }
          fundingSources
          fiscalYear
          workUnitName
          procurementMethod
          procurementTypes
          locations
          kodeSatker
          instituteName
        }
        logs {
          activity
          actor {
            isSystem
            userInfo {
              id
              name
              username
            }
          }
          before {
            key
            value
          }
          after {
            key
            value
          }
          id
          timestamp
        }
      }
      ... on GenericError {
        __typename
        code
        message
        reqId
      }
    }
  }
`)
