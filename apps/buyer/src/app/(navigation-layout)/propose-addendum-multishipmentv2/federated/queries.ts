import { graphql } from "@/federatedGql"

export const ORDER_DETAIL_ADDENDUM_MULTISHIPMENT = graphql(`
  query orderDetailForAddendumMultishipmentv2($input: OrderInput!) {
    orderDetailV2(input: $input) {
      ... on GenericError {
        __typename
        code
        reqId
        message
      }
      ... on OrderV2 {
        id
        orderKey
        status
        total
        totalWithoutTax
        longestPreorderSLA
        roundingMethod
        type
        addenda {
          id
        }
        additionalFee {
          ppn {
            total
          }
          ppnBm
        }
        isFtz
        items {
          id
          qty
          lastPrice
          notes
          tax {
            ppnPercentage
            ppnBmPercentage
            taxableRate
          }
          addOns {
            id
            addOnVariantId
            type
            lastPrice
            tax {
              ppnPercentage
              taxableRate
            }
          }
          snapshot {
            id
            name
            addOns {
              name
              productAddOnVariants {
                id
                name
              }
            }
            images {
              imageUrl
            }
            stockUnit {
              primaryUnit
            }
            shipping {
              weight
              weightUnit
            }
            variants {
              optionValues
            }
            prices {
              productWholesalePrices {
                minQuantity
                price
              }
              selectedRegionPrice {
                regionName
              }
            }
          }
        }
        payments {
          id
          phase
          deliveryOrderGroupId
        }
        shipmentSummary {
          provider
        }
      }
    }
  }
`)

export const ORDER_DELIVERY_BY_GROUP_ADDENDUM_MULTISHIPMENT = graphql(`
  query deliveryOrderByGroupAddendumMultishipmentv2($groupId: ID!) {
    deliveryOrderGroup(groupId: $groupId) {
      ... on GenericError {
        __typename
        code
        reqId
        message
      }
      ... on DeliveryOrderGroup {
        id
        deliveryOrders {
          id
          logisticName
          requestDateArrival
          estimationTime {
            minDay
            maxDay
          }
          detailPrice {
            shipmentFeeWithoutPPN
            insuranceFeeWithoutPPN
            ppnPercentage
            ppnPercentageInsurance
            ppnShipmentValue
            ppnInsuranceValue
            taxableRate
          }
          volumetric {
            weight
            weightUnit
          }
        }
      }
    }
  }
`)

export const ORDER_DOCUMENTS_ADDENDUM = graphql(`
  query orderDocumentsAddendum($orderId: String!) {
    orderDocuments(orderId: $orderId) {
      ... on OrderDocumentsResponse {
        __typename
        letters {
          category
          isApproved
          clauses
        }
      }
      ... on GenericError {
        __typename
      }
    }
  }
`)

export const ORDER_CALCULATOR_PPN = graphql(`
  query orderCalculatorPPN($input: CalculatorPPNInput!) {
    orderCalculatorPPN(input: $input) {
      ... on CalculatorPPNResponse {
        __typename
        totalAllPpnBm
        totalAllPpn
        grandTotalBeforeTax
        grandTotalAfterTax
      }
      ... on GenericError {
        __typename
        reqId
        message
        code
      }
    }
  }
`)
