"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useRouter } from "next/navigation"
import {
  AddendumProductSection,
  AddendumReasonSection,
  AddendumArrivalDateSection,
  AddendumShippingCostSection,
  AddendumButtonSection,
  AddendumInfo,
  AddendumAlert,
  AddendumAdditionalClauseSection,
  AddendumSummarySection,
} from "order-ui"
import { useEffect, useState } from "react"
import {
  FormProvider,
  type SubmitHandler,
  useForm,
  useWatch,
} from "react-hook-form"
import { Spinner, useSnackbar } from "shared-ui"

import constructAddendumData from "./helpers/constructAddendumData"
import constructProposeAdendumInput from "./helpers/constructProposeAddendumInput"
import convertInputOrderCalculationPPN from "./helpers/convertInputOrderCalculationPPN"
import getEligibleRequestArrivalDateStartDate from "./helpers/getEligibleRequestArrivalDateStartDate"
import transformAddendumData from "./helpers/transformAddendumData"
import useGetDeliveryOrderByGroup from "./hooks/useGetDeliveryByGroup"
import useGetListPpn from "./hooks/useGetListPPN/useGetListPPN"
import useGetOrderDetailAddendumMultishipment from "./hooks/useGetOrderDetailAddendumMultishipment"
import useGetOrderDocumentsAddendum from "./hooks/useGetOrderDocumentsAddendum"
import useOrderCalculatorPPN from "./hooks/useOrderCalculatorPPN"
import useProposeAddendum from "./hooks/useProposeAddendum/useProposeAddendum"
import {
  type TAddendumProposalMultishipmentSchema,
  addendumProposalMultishipmentSchema,
} from "./schema"
import TitleWithBackButton from "../(sidebar-layout)/components/TitleWithBackButton"
import useGetOrderActionButton from "../(sidebar-layout)/order-multishipment/[id]/hooks/useGetOrderActionButton"
import {
  OrderActionButton,
  type OrderItem,
  OrderStatus,
  type OrderV2,
  OrderType,
} from "@/federatedGql/graphql"

export type TAddendumFormActiveSection = {
  productQty: boolean
  shippingCost: boolean
  additionalClause: boolean
  taxValue: boolean
  arrivalDate: boolean
  addons: boolean
}

type TProposeNewAddendum = {
  activeSection: TAddendumFormActiveSection
  orderId: string
  orderKey: string
  shipmentId: string
}

export default function ProposeAddendumMultishipment({
  activeSection,
  orderId,
  orderKey,
  shipmentId,
}: Readonly<TProposeNewAddendum>) {
  const router = useRouter()
  const { enqueueSnackbar } = useSnackbar()
  const [isEditing, setIsEditing] = useState<boolean>(true)

  const {
    data: dataActionButtons,
    isLoading: isLoadingGetActionButtons,
    isError: isErrorActionButton,
  } = useGetOrderActionButton({
    orderId,
    options: {
      enabled: !!orderId,
    },
  })

  const {
    data: orderData,
    isFetching: isFetchingOrderData,
    isError: isErrorOrderData,
  } = useGetOrderDetailAddendumMultishipment({
    input: {
      id: orderId,
      orderKey,
    },
  })

  const isOrderJasaAndDigital = orderData?.type === OrderType.ServicesAndDigital

  const { data: itemPPNList } = useGetListPpn({
    options: {
      enabled: activeSection.taxValue,
    },
  })

  const {
    data: deliveryOrderByGroupIdData,
    isFetching: isFetchingDeliveryOrderByGroup,
    isError: isErrorDeliveryOrderByGroup,
  } = useGetDeliveryOrderByGroup({
    deliveryGroupId: orderData?.payments[0]?.deliveryOrderGroupId ?? "",
  })
  const firstShipmentData = deliveryOrderByGroupIdData?.deliveryOrders[0]

  const { data: orderDocumentsData } = useGetOrderDocumentsAddendum({
    orderId,
  })

  const formMethods = useForm<TAddendumProposalMultishipmentSchema>({
    mode: "onSubmit",
    reValidateMode: "onChange",
    resolver: zodResolver(addendumProposalMultishipmentSchema),
    defaultValues: constructAddendumData({
      orderData,
      activeSection,
      deliveryOrderByGroupIdData,
      orderDocumentsData,
    }),
  })

  const {
    setValue,
    getValues,
    reset: resetForm,
    handleSubmit,
    formState,
  } = formMethods

  const isPPN11 = useWatch({
    control: formMethods.control,
    name: "isPPN11",
  })

  const {
    mutate: submitAddendumProposal,
    isLoading: isLoadingSubmitAddendumProposal,
  } = useProposeAddendum({})

  const isProductSectionActive =
    activeSection.productQty || activeSection.taxValue || activeSection.addons

  const isOrderDelivered = orderData?.status === OrderStatus.Delivered

  const {
    mutate: mutateOrderCalculatorPPN,
    isLoading: isLoadingCalculatorPPN,
  } = useOrderCalculatorPPN({
    onSuccess: (data) => {
      setValue("priceSummary.newPrice", {
        grandTotalAfterTax: data.grandTotalAfterTax,
        grandTotalBeforeTax: data.grandTotalBeforeTax,
        totalAllPpn: data.totalAllPpn,
        totalAllPpnBm: data.totalAllPpnBm,
      })
      setIsEditing(false)
    },
    onError: (err) => {
      enqueueSnackbar({
        message:
          err.message ??
          "Terjadi Kesalahan saat kalkulasi pajak. Silakan coba beberapa saat lagi.",
        type: "error",
      })
    },
  })

  useEffect(() => {
    resetForm(
      constructAddendumData({
        orderData,
        activeSection,
        deliveryOrderByGroupIdData,
        orderDocumentsData,
      })
    )
  }, [orderData, activeSection, orderDocumentsData, deliveryOrderByGroupIdData])

  const onClickPreviewAddendum = () => {
    const addendumData = getValues()
    const shipmentData =
      deliveryOrderByGroupIdData?.deliveryOrders?.[0]?.detailPrice

    // update data form
    const newAddendumData = transformAddendumData(addendumData)

    resetForm(newAddendumData)

    // input mutateOrderCalculatorPPN
    const input = convertInputOrderCalculationPPN({
      addendumData: newAddendumData,
      orderData,
      shipmentData,
    })
    mutateOrderCalculatorPPN(input)
  }

  const startDate =
    activeSection.arrivalDate && orderData
      ? getEligibleRequestArrivalDateStartDate(orderData, firstShipmentData)
      : undefined

  const submitProposeAddendum: SubmitHandler<
    TAddendumProposalMultishipmentSchema
  > = (newOrder: TAddendumProposalMultishipmentSchema) => {
    submitAddendumProposal(
      {
        input: constructProposeAdendumInput({
          newOrder,
          oldOrder: orderData,
          activeSection,
          shipmentId: shipmentId,
        }),
      },
      {
        onSuccess(res) {
          router.push(
            // NOTES: Latest addendum might not be created immediately
            // pass addendumId so we can guarantee the latest addendum doc to be opened
            `/sign-document/addendum-letter?id=${orderId}&key=${orderKey}&addendumId=${res.id}`
          )
        },
      }
    )
  }

  const isFetching =
    isFetchingOrderData ||
    isFetchingDeliveryOrderByGroup ||
    isLoadingGetActionButtons

  if (isFetching) {
    return (
      <div className="flex h-[50vh] w-full items-center justify-center">
        <Spinner color="primary" size="large" />
      </div>
    )
  }

  const isError =
    isErrorOrderData ||
    isErrorDeliveryOrderByGroup ||
    !orderData ||
    !deliveryOrderByGroupIdData ||
    !firstShipmentData ||
    isErrorActionButton

  if (
    isError ||
    [OrderStatus.AddendumDraft].includes(orderData.status) ||
    !dataActionButtons?.order?.includes(OrderActionButton.ProposeAddendum)
  ) {
    return (
      <AddendumInfo
        orderData={orderData as OrderV2}
        dataActionButtons={dataActionButtons}
        isError={isError}
      />
    )
  }

  return (
    <div className="mx-16 my-10">
      <TitleWithBackButton
        onClick={() => window.history.back()}
        title="Pengajuan Adendum"
      />
      <AddendumAlert isPPN11={isPPN11?.items || isPPN11?.shipment} />
      <FormProvider {...formMethods}>
        <div>
          {(isProductSectionActive || isPPN11?.items) && (
            <AddendumProductSection
              isAbleToEditQty={activeSection.productQty}
              isAbleToEditPPN={activeSection.taxValue}
              isAbleToRemoveAddOn={activeSection.addons}
              isEditing={isEditing}
              disabledIncrementIfOrderHasDelivered={isOrderDelivered}
              listPpn={itemPPNList}
              fieldName={"items"}
              isPPN11={isPPN11?.items}
            />
          )}
          {!isOrderJasaAndDigital &&
            (activeSection.shippingCost ||
              activeSection.taxValue ||
              isPPN11?.shipment) && (
              <AddendumShippingCostSection
                isEditing={isEditing}
                fieldName="shipment.newPrice"
                oldFieldName="shipment.oldPrice"
                isNewPriceFieldName="shipment.isNewPrice"
                isAbleToEditPPN={activeSection.taxValue}
                isAbleToEditShippingCost={activeSection.shippingCost}
                ppnFieldName="shipment.newPPN"
                oldPpnFieldName="shipment.oldPPN"
                isNewPPNFieldName="shipment.isNewPPN"
                listPpn={itemPPNList}
                isPPN11={isPPN11?.shipment}
              />
            )}
          {activeSection.arrivalDate && (
            <AddendumArrivalDateSection
              isEditing={isEditing}
              fieldName="arrivalDate.new"
              oldFieldName="arrivalDate.old"
              startDate={startDate}
            />
          )}
          {activeSection.additionalClause && (
            <AddendumAdditionalClauseSection
              isEditing={isEditing}
              fieldName="additionalClause.new"
              oldFieldName="additionalClause.old"
            />
          )}

          {!isEditing &&
            (isProductSectionActive || isPPN11?.items || isPPN11?.shipment) && (
              <AddendumSummarySection
                orderItems={orderData?.items as unknown as OrderItem[]}
                priceSummary={getValues("priceSummary")}
                fieldName="items"
                shipmentFieldName="shipment"
                orderKey={orderData?.orderKey}
                orderId={orderData?.id}
                shipmentDetail={firstShipmentData}
              />
            )}
        </div>
        <AddendumReasonSection isEditing={isEditing} fieldName="reason" />
      </FormProvider>

      <AddendumButtonSection
        isEditing={isEditing}
        onSetEditingMode={setIsEditing}
        onButtonSubmit={handleSubmit(submitProposeAddendum)}
        disableButtonSubmit={isLoadingSubmitAddendumProposal}
        onClickButtonPreview={onClickPreviewAddendum}
        disableButtonPreview={!formState.isValid || isLoadingCalculatorPPN}
      />
    </div>
  )
}
