import "server-only"

import { type TypedDocumentNode } from "@graphql-typed-document-node/core"
import { GraphQLClient } from "graphql-request"
import {
  type RequestMiddleware,
  type VariablesAndRequestHeadersArgs,
  type Variables,
} from "graphql-request/build/esm/types"
import { type NextApiRequest } from "next"
import { getServerSession } from "next-auth"
import { type TRequest } from "shared-utils"
import { graphqlRscMiddleware } from "shared-utils/accessToken"

import { responseMiddleware } from "./graphqlClient"
import { authOptions } from "@/authentication/authOptions"
import { GRAPHQL_URL } from "@/config/api"

const middleware: RequestMiddleware = async (request) => {
  const resultMiddleware = graphqlRscMiddleware({
    request: request as TRequest,
    getServerSession: () =>
      getServerSession(
        authOptions({ req: request as NextApiRequest, forceRefresh: true })
      ),
  })

  return resultMiddleware
}

const graphqlRequest = new GraphQLClient(
  process.env.SSR_TYK_URL || GRAPHQL_URL,
  {
    requestMiddleware: middleware,
    responseMiddleware: responseMiddleware,
    credentials: "include",
  }
)

/**
 * graphqlRscRequest should only be used from a Server Component.
 */
export const graphqlRscRequest = async <T, V extends Variables = Variables>(
  document: TypedDocumentNode<T, V>,
  ...variablesAndRequestHeaders: VariablesAndRequestHeadersArgs<V>
): Promise<T> => {
  const response = await graphqlRequest.request(
    document,
    ...variablesAndRequestHeaders
  )
  return response
}
