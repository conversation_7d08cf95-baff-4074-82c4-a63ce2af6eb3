"use client";

import dynamic from "next/dynamic";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Edit2 } from "react-feather";
import { Divider, But<PERSON>, useSnackbar } from "shared-ui";

import ErrorBoundary from "@/components/error-boundary";

import { LabelText } from "./LabelText";
import AlertHelpCenter from "../../components/AlertHelpCenter";
import EmptyPage from "../../components/EmptyPage";
import { type TAccountInfoResponse } from "../types";
import { ZENDESK_URL } from "@/config/client";
import { Profession, type ContractUser } from "@/federatedGql/graphql";
import { usePathname } from "@/navigation";

const ChangeASNModal = dynamic(() => import("./ChangeASNModal"));

type TAccountProfile = {
  accountInfoData: TAccountInfoResponse;
  PrivyData?: ContractUser | null;
  isAllowEditNIP: boolean;
};

const AccountProfile = ({
  accountInfoData,
  PrivyData,
  isAllowEditNIP,
}: TAccountProfile) => {
  const searchParams = useSearchParams()?.get("success");

  const { enqueueSnackbar } = useSnackbar();
  const [showModal, setShowModal] = useState<"changeASN" | "editNIP" | "">();

  const pathname = usePathname();

  useEffect(() => {
    if (searchParams === "phone") {
      enqueueSnackbar({
        type: "success",
        message: "Nomor telepon berhasil diubah.",
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOnClickZendesk = (type: "email" | "phone") => {
    const queryParams = new URLSearchParams({
      tf_7052874143887: "manajemen_akun_terpusat_spse",
      tf_11719974953871: accountInfoData?.email ?? "",
      tf_11720303669519: "zzz_account_success",
      tf_11724319848975: "zzz_profile_success",
      tf_11725221771663: "zzz_req_edit_yes",
      tf_11725245424527: type === "email" ? "zzz_edit_email" : "zzz_edit_phone",
    });

    window.open(
      `${ZENDESK_URL}/requests/new?${queryParams.toString()}`,
      "_blank"
    );
  };

  const renderEditButton = (
    value: string,
    type: "email" | "phone" | "nip" | "employment_status",
    callbackOnClick?: () => void
  ) => {
    if (
      (type === "nip" &&
        !(accountInfoData?.type === Profession.Asn && isAllowEditNIP)) ||
      (type === "employment_status" &&
        !(accountInfoData?.type === Profession.NonAsn && isAllowEditNIP))
    ) {
      return value;
    }
    return (
      <div className="flex items-center">
        <span className="mr-1 text-tertiary500">{value}</span>
        <Button
          id={`btn-edit-${type}`}
          variant="transparent"
          IconLeft={type === "employment_status" ? undefined : Edit2}
          onClick={callbackOnClick}
        >
          {type === "employment_status" ? "Daftar sebagai ASN" : "Ubah"}
        </Button>
      </div>
    );
  };

  return (
    <div>
      <ErrorBoundary>
        <AlertHelpCenter pathname={pathname} />
        {!accountInfoData ? (
          <EmptyPage page="account-profile" />
        ) : (
          <>
            <div className="mb-6 rounded-4 border border-tertiary50 p-6">
              <h1 className="text-lg font-bold text-tertiary500">
                Informasi Identitas Digital
              </h1>
              <Divider className="my-4" />
              <div className="flex flex-col gap-2">
                <LabelText
                  label="Email"
                  custom={renderEditButton(
                    accountInfoData.email ?? "",
                    "email",
                    () => handleOnClickZendesk("email")
                  )}
                />
                <LabelText
                  label="Nomor Telepon"
                  custom={renderEditButton(
                    accountInfoData.phone ?? "",
                    "phone",
                    () => handleOnClickZendesk("phone")
                  )}
                />
              </div>
            </div>
            <div className="rounded-4 border border-tertiary50 p-6">
              <p className="text-lg font-bold text-tertiary500">
                Informasi Profil
              </p>
              <Divider className="my-4" />
              <div className="flex w-full flex-row">
                <div className="flex basis-1/2 flex-col gap-2">
                  <LabelText
                    label="Username"
                    value={accountInfoData.username}
                  />
                  <LabelText label="NIK" value={accountInfoData.profile?.nik} />
                  {!!PrivyData?.providerUserId && (
                    <LabelText
                      label="PrivyID"
                      value={PrivyData.providerUserId.toUpperCase()}
                    />
                  )}
                </div>
                <div className="flex basis-1/2 flex-col gap-2">
                  <div>
                    <LabelText
                      label="Status Kepegawaian"
                      custom={renderEditButton(
                        accountInfoData.type?.replace("_", " ") || "-",
                        "employment_status",
                        () => setShowModal("changeASN")
                      )}
                    />
                  </div>
                  <div>
                    <LabelText
                      label="NIP/NRP"
                      custom={renderEditButton(
                        accountInfoData.profile?.nip || "-",
                        "nip",
                        () => setShowModal("editNIP")
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>
            {(showModal === "changeASN" || showModal === "editNIP") && (
              <ChangeASNModal
                open={!!showModal}
                keyModal={showModal}
                onClose={() => setShowModal("")}
                oldNIP={accountInfoData.profile?.nip}
              />
            )}
          </>
        )}
      </ErrorBoundary>
    </div>
  );
};

export default AccountProfile;
