import { IS_CANARY_PREPROD, IS_PRODUCTION } from "shared-utils";

export const SHOW_PPSDM_FIELD = "ACCOUNT-SHOW_PPSDM_FIELD";

export const SHOW_NON_INTEGRATION_SIRUP =
  "ACCOUNT-KILL_SWITCH_INTEGRATION_SIRUP";

export const SHOW_SOCKET_GET_UPLOAD_STATUS_V1 =
  "ACCOUNT-KILL_SWICTH_AUTH_UPLOAD_STATUS_SOCKET";

export const SHOW_INTEGRATE_STAFF = "ACCOUNT-STAFF_GRADUAL_RELEASE";

export const USE_GOOGLE_MAP_VERSION_2 = "USE_GOOGLE_MAP_VERSION_2";

export const SHOW_OMNYX_NEW_PROFILE_FLOW =
  "ACCOUNT-NEW_PROFILE_NON_PENYEDIA_OMNYX_FLOW";

export const WHITELIST_KLPD_VAI = "ACCOUNT-WHITELIST_KLPD_VAI";

export const FLAG_VALIDASI_KSWP_FROM_DJP = !IS_PRODUCTION;

export const ENTERPRISE_VALIDATION = "ACCOUNT-NONKLPD_ENTERPRISE_VALIDATION";

export const FLAG_INFO_ADMIN_WAITING_VERIFICATION = false; //Note: ON HOLD

export const SHOW_PINPOINT_LEAFLET = "ACCOUNT-PINPOINT_LEAFLET";

export const LOGISTIC_IGNORE_DISTRICT_VILLAGE =
  "LOGISTIC_IGNORE-DISTRICT-VILLAGE";

export const FLAG_DEAKTIVASI_PERSONA = !IS_PRODUCTION || IS_CANARY_PREPROD;

export const FLAG_AKTIVASI_PERSONA = !IS_PRODUCTION || IS_CANARY_PREPROD;

export const FLAG_INFORMATION_REJECTED_USER_ASN =
  !IS_PRODUCTION || IS_CANARY_PREPROD;

export const FLAG_IMPROVEMENT_LANDING_PAGE = !IS_PRODUCTION;

export const FLAG_RESET_PHOTO_VERIFICATION = !IS_PRODUCTION;

export const FLAG_ALERT_ERROR_NIB = !IS_PRODUCTION;

export const FLAG_INFORMATION_FIELD_KURATOR = !IS_PRODUCTION;

export const FLAG_INFORMATION_FIELD_AUDITOR = !IS_PRODUCTION;
export const FLAG_KURATOR = !IS_PRODUCTION;
