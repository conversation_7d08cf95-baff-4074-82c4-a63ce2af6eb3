import { graphql } from "@/federatedGql/gql"

export const KBLI_BY_INSTITUTION_ID = graphql(`
  query kbliByInstutionId($id: String!) {
    getInstitutionByID(id: $id) {
      ... on Institution {
        __typename
        company {
          oss {
            project {
              kbli {
                kbli
                name
                id
              }
            }
          }
        }
      }
      ... on GenericError {
        __typename
        code
        message
        reqId
      }
    }
  }
`)

export const MINICOM_COMPETITION_DETAIL_QUERY = graphql(`
  query minikomCompetitionDetail($input: CompetitionDetailInput!) {
    minikomCompetitionDetail(input: $input) {
      ... on Competition {
        __typename
        id
        key
        title
        competitionNumber
        publishedAt
        status
        total
        reason
        sellerOptedIn
        isKonstruksi
        payment {
          description
          type
        }
        documents {
          name
          size
          type
          url
        }
        logs {
          activity
          actor {
            isSystem
            userInfo {
              id
              name
              username
            }
          }
          before {
            key
            value
          }
          after {
            key
            value
          }
          id
          timestamp
        }
        proposals {
          id
          status
          roundingMethod
          total
          totalWithTax
          sellerInfo {
            institusiId
            shippingAddresses {
              snapshotId
              villageAreaCode
              latitude
              longitude
            }
          }
          items {
            id
            competitionItemId
            isSelected
            price
            priceWithTax
            productId
            productVariantId
            qty
            status
            subTotal
            subTotalWithTax
            tax {
              ppnBmPercentage
              ppnPercentage
              taxableRate
            }
            tkdn
            shipment {
              rateId
              fee
              insuranceFee
              provider
              service
              slaMax
              slaMin
              subTotal
              subTotalWithTax
              tax {
                ppnPercentage
                taxableRate
              }
            }
            productSnapshot {
              id
              type
              name
              version
              username
              slug
              variants {
                options
                optionValues
                id
                price
                priceWithTax
                stock
                isActive
              }
              prices {
                selectedRegionPrice {
                  price
                  regionName
                }
              }
              sellerId
              images {
                id
                imageUrl
              }
              category {
                id
              }
              shipping {
                weight
                weightUnit
                height
                width
                length
                dimensionUnit
              }
              productAddOns {
                id
                name
                objectType
                type
                tax {
                  ppnPercentage
                  ppnTypes
                  taxableRate
                }
                productAddOnVariants {
                  id
                  name
                  price
                  priceWithTax
                  taxablePrice
                }
              }
            }
            addOnVariants {
              tax {
                ppnPercentage
                taxableRate
              }
              addOnVariantId
              id
              price
              priceWithTax
              qty
              subTotal
              subTotalWithTax
              type
            }
          }
          documents {
            name
            size
            type
            url
            token
          }
        }
        sellerQualification {
          skalaUsaha {
            company
            oss
          }
          isItemized
        }
        buyerInfo {
          user {
            personaDetail {
              satker {
                namaKlpd
              }
            }
          }
          reviewer {
            userInfo {
              id
              name
              username
            }
            personaDetail {
              personaId
              persona {
                institusiId
                userId
                appUserName
                unit
              }
              satker {
                namaKlpd
                namaSatker
                kodeSatker
              }
            }
          }
        }
        schedule {
          endPeriod
          startPeriod
        }
        shipments {
          id
          destination {
            snapshotId
            latitude
            longitude
            villageAreaCode
            regionDetail {
              cityName
              districtName
              provinceName
              villageName
            }
          }
          requestArrivalDate
        }
        items {
          hasReviewerApproved
          sectoralProduct {
            name
            materials {
              coefficient
              name
            }
            tools {
              name
              coefficient
            }
            workerPackages {
              name
              coefficient
            }
          }
          jobName
          category {
            id
            name
            type
            productType
            productSubType
            kbli
            parent {
              name
              parent {
                name
              }
            }
          }
          id
          price
          qty
          specs {
            defaultValue
            id
            name
            type
            value
          }
          unit
          orderId
          orderKey
          jobName
          sectoralProduct {
            name
            tools {
              id
              name
              coefficient
            }
            materials {
              id
              name
              coefficient
            }
            workerPackages {
              id
              name
              coefficient
            }
          }
        }
        rup {
          id
          rupCode
          name
          totalBalance
          fundingSourceDetails {
            mak
          }
          fundingSources
          fiscalYear
          workUnitName
          procurementMethod
          procurementTypes
          locations
          kodeSatker
          instituteName
        }
        logs {
          activity
          actor {
            isSystem
            userInfo {
              id
              name
              username
            }
          }
          before {
            key
            value
          }
          after {
            key
            value
          }
          id
          timestamp
        }
      }
      ... on GenericError {
        __typename
        code
        message
        reqId
      }
    }
  }
`)
