import { cx } from "class-variance-authority"
import { useRouter } from "next/navigation"
import { Modal, type ModalProps } from "shared-ui"

import OrderItem from "./OrderItem"
import { type TCompetitionDetailServerQueryRes } from "../../../serverRequests/minicomCompetitionDetailServerQuery"

type TOrderDetailModalModalProps = ModalProps & {
  onClose: () => void
  products: TCompetitionDetailServerQueryRes["items"]
}

const OrderDetailModal = ({
  onClose,
  open,
  products,
}: TOrderDetailModalModalProps) => {
  const router = useRouter()
  if (products.length < 1) return null
  const groupedOrderDetail = products.reduce(
    (
      orderDetails: {
        orderId: string
        orderKey: string
        names: string[]
        hasReviewerApproved: boolean
      }[],
      product
    ) => {
      let group = orderDetails.find(
        (detail) => detail.orderId === product.orderId
      )

      if (!group && !!product.orderId && !!product.orderKey) {
        group = {
          orderId: product.orderId,
          orderKey: product.orderKey,
          names: [],
          hasReviewerApproved: product.hasReviewerApproved,
        }
        orderDetails.push(group)
      }

      group?.names.push(product.category.name)

      return orderDetails
    },
    []
  )

  const handleRedirect = (orderId: string, orderKey: string) => {
    router.push(`/order/detail?id=${orderId}&orderKey=${orderKey}`)
  }

  return (
    <Modal
      modalId="modal-orderDetail-competition"
      title="Pilih Pesanan"
      classNames={{
        content: cx(
          "w-[528px] !py-6 !px-0",
          groupedOrderDetail.length > 0 ? "h-[454px]" : "h-[252px]"
        ),
        header: "mb-6 !px-6",
      }}
      open={open}
      onOpenChange={onClose}
      closable={false}
    >
      <div className="flex flex-col gap-y-5 overflow-y-auto scroll-smooth px-6">
        {groupedOrderDetail.length > 0
          ? groupedOrderDetail.map((order, idx) => (
              <OrderItem
                key={`${order.orderId}-${idx}`}
                order={order}
                onClick={
                  order.hasReviewerApproved
                    ? () => handleRedirect(order.orderId, order.orderKey)
                    : undefined
                }
              />
            ))
          : "Tidak ada pesanan"}
      </div>
    </Modal>
  )
}

export default OrderDetailModal
