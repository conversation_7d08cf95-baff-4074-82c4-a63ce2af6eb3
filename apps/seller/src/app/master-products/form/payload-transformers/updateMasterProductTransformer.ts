import { type TMasterProductForm } from "../schema"
import mapDocuments from "@/app/products/form/helpers/mapDocuments"
import { mapInformationsFieldCreate } from "@/app/products/form/helpers/mapInformationsField"
import {
  ProductDimensionUnit,
  ProductWeightUnit,
  type UpdateMasterProductInputV2,
} from "@/federatedGql/graphql"

type THandleUpdateMasterProductTransformerProps = {
  id: string
  data: TMasterProductForm
}

const updateMasterProductTransformer = ({
  id,
  data,
}: THandleUpdateMasterProductTransformerProps): UpdateMasterProductInputV2 => {
  return {
    id,
    name: data.name || "",
    description: data.description || "",
    category: {
      id: data.category?.id || "",
    },
    images: data.images.map((image) => ({
      id: image.id,
      imageToken: image.imageToken,
      imageUrl: image.imageUrl,
    })),
    informations: {
      mainInformations: mapInformationsFieldCreate(
        data.productInformations.mainInformations
      ),
      additionalInformations: mapInformationsFieldCreate(
        data.productInformations.additionalInformations
      ),
      documents: mapDocuments(data.productInformations.documents) ?? undefined,
    },
    options: data?.options ?? [],
    variants: !data.priceAndVariantField.isZonePricingActive
      ? data.priceAndVariantField.variants.map((variant) => ({
          ...variant,
          id: variant.id,
          priceLowerBound: variant.priceLowerBound,
          priceUpperBound: variant.priceUpperBound,
          sku: variant.sku || "",
          options: variant.options || undefined,
          optionValues: variant.optionValues || undefined,
        }))
      : [],
    regionPrices: data.priceAndVariantField.isZonePricingActive
      ? data.priceAndVariantField.regionPrices?.map((regionPrice) => ({
          id: regionPrice.id,
          priceLowerBound: regionPrice.priceLowerBound,
          priceUpperBound: regionPrice.priceUpperBound,
          regionCode: regionPrice.regionCode,
        })) ?? undefined
      : undefined,
    shipping: {
      width: data.shipping?.width || 0,
      height: data.shipping?.height || 0,
      length: data.shipping?.length || 0,
      weight: data.shipping?.weight || 0,
      dimensionUnit: ProductDimensionUnit.Cm,
      weightUnit: ProductWeightUnit.Gr,
    },
    kbki: data.kbki || "",
    pdn: {
      type: data.pdn.type,
      laborType: data.pdn.laborType,
      locationType: data.pdn.locationType,
      materialType: data.pdn.materialType,
      countryCode: data.pdn.countryCode,
    },
    tkdn:
      data.tkdn?.isActive && data.tkdn?.number
        ? {
            number: data.tkdn?.number,
            specificationId: data.tkdn?.specificationId,
          }
        : undefined,
    sni: data.sni?.isActive
      ? {
          brand: data.sni.brand ?? "",
          certificateNumber: data.sni.certificateNumber ?? "",
          sniNumber: data.sni.sniNumber ?? "",
          spptNumber: data.sni.spptNumber,
          companyName: data.sni.companyName,
        }
      : undefined,

    tax: {
      ppnPercentage: data.tax.ppnPercentage,
      ppnBmId: data.tax.isPpnBmActive
        ? data.tax.isPpnBmVehicle
          ? data.tax.ppnBmVehicleId
          : data.tax.ppnBmId
        : undefined,
    },
    access: data.access,
    principal: { id: data.principal?.id ?? "" },
    priceScheme: data.priceAndVariantField.priceScheme,
  }
}

export default updateMasterProductTransformer
