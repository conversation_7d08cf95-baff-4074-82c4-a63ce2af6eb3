"use client"

import { type TGetSignedUrl } from "shared-utils"

import { type TFileImages } from "@/components/upload-group-image/type"
import UploadProductImage from "@/components/upload-product-image"

import { type TFormProductImages } from "./schema/productImagesSchema"
import { PRODUCT_IMAGE_CONFIG } from "../../constant"

type TUploadProductImageFieldProps = {
  errorMessage?: string
  onChange: (event: TFileImages[]) => void
  onDelete: (index: number) => void
  value: TFormProductImages
  onDragImage: (fromIndex: number, toIndex: number) => void
  onError: (err: string) => void
  disabled?: boolean
  disableDrag?: boolean
  uploadImageHooks: {
    file?: File
    percentage: number
    handleUpload: (paramFiles: FileList | null) => void
    cancel: () => void
    status: "error" | "idle" | "loading" | "success"
    signedUrlInfo?: TGetSignedUrl
  }[]
}

const UploadProductImageField = ({
  errorMessage = "",
  onChange,
  onDelete,
  onError,
  value,
  onDragImage,
  uploadImageHooks,
  disabled = false,
  disableDrag = false,
}: TUploadProductImageFieldProps) => {
  const {
    format,
    minHeight,
    maxHeight,
    minWidth,
    maxWidth,
    maxLimitImages,
    maxSize,
  } = PRODUCT_IMAGE_CONFIG

  return (
    <UploadProductImage
      uploadImageHooks={uploadImageHooks}
      accept={format}
      error={errorMessage}
      isFailedUpload={false}
      maxHeight={maxHeight}
      maxSize={maxSize}
      maxWidth={maxWidth}
      onChange={onChange}
      onDelete={onDelete}
      onError={onError}
      minHeight={minHeight}
      minWidth={minWidth}
      onCancel={() => undefined}
      onDragImage={onDragImage}
      onPreviewFile={() => undefined}
      timeRemaining={0}
      value={value}
      maxLimitImages={maxLimitImages}
      disabled={disabled}
      disableDrag={disableDrag}
      progress={[]}
      isUploading={false}
    />
  )
}

export default UploadProductImageField
