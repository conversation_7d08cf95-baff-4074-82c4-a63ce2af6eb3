/* eslint-disable @typescript-eslint/no-use-before-define */
import { useEffect } from "react"
import { useFormContext, useWatch, useFieldArray } from "react-hook-form"

import Fieldset from "@/components/fieldset"
import SectionContainer from "@/components/section-container"
import { type TFileImages } from "@/components/upload-group-image/type"
import { type TFileVideo } from "@/components/upload-video/type"

import ProductDescriptionField from "./ProductDescription"
import ProductNameField from "./ProductName"
import URLVideoField from "./URLVideo"
import UploadProductImageField from "./UploadProductImage"
import UploadProductVideoField from "./UploadProductVideo"
import useUploadProductImage from "./hooks/useUploadProductImage"
import useUploadProductVideo from "./hooks/useUploadProductVideo"
import { PRODUCT_IMAGE_CONFIG } from "../../constant"
import { type TFormSchema } from "../../schema/formSchema"
import ListSectoralProductField from "../ListSectoralProductField"
import { VideoSourceType, CategoryType } from "@/federatedGql/graphql"
import { useProductStore } from "@/store/productStore"
import convertToFileList from "@/utils/convertToFileList"

const ProductInfoField = () => {
  const {
    setValue,
    getValues,
    formState: { errors, isSubmitSuccessful },
    clearErrors,
    setError,
    control,
  } = useFormContext<TFormSchema>()

  const {
    minHeight,
    maxHeight,
    minWidth,
    maxWidth,
    optimalWidth,
    optimalHeight,
    maxLimitImages,
    minLimitImages,
  } = PRODUCT_IMAGE_CONFIG

  const isConsolidationProduct = Boolean(getValues("consolidation.isActive"))

  // Define a function to create upload image hooks
  const createUploadImageHook = () =>
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useUploadProductImage({
      onSuccessUpload: (result, file) => {
        if (result.token && file) {
          const newImages = {
            imageToken: result.token,
            imageUrl: URL.createObjectURL(file),
            name: file.name,
          }
          append(newImages)
        }
      },
      onError: (err) => handleErrorImage(err),
    })

  const uploadImageHooks = Array.from({ length: 5 }, createUploadImageHook)

  const { storedProduct, updateStoredProduct, clearStoredProduct } =
    useProductStore()

  const isCategoryTypeSpecial =
    useWatch({ control, name: "category.type" }) === CategoryType.Special

  const masterProductId = useWatch({ control, name: "masterProductId" })

  const {
    fields: productImages,
    append,
    remove,
    swap,
  } = useFieldArray({
    control,
    name: "images",
    keyName: "imageId",
  })

  useEffect(() => {
    if (masterProductId) {
      uploadImageHooks.forEach((hook) => hook?.reset())
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [masterProductId])

  const handleErrorImage = (message: string) => {
    setError("images", {
      message,
    })
  }

  const handleErrorVideo = (message: string) => {
    setError("video", {
      message,
    })
  }

  const handleOnErrorVideoUpload = (error: string) => {
    if (!error) {
      clearErrors(`video`)
      return
    }
    setError("video", {
      message: error,
    })
    updateStoredProduct({
      video: undefined,
      videoSource: undefined,
      videoUrl: undefined,
    })
  }

  const {
    handleUpload: handleVideoUpload,
    reset: resetVideoUpload,
    percentage: videoUploadPercentage,
    estimatedTimeLeft: videoUploadEstimatedTimeLeft,
    cancel: cancelVideoUpload,
  } = useUploadProductVideo({
    onError: handleOnErrorVideoUpload,
    onSuccessUpload: (result, file) => {
      if (result.token && file) {
        setValue("videoToken", result.token)
      }
    },
  })

  const handleProductImageChange = (event: TFileImages[]) => {
    const dataTransfer = new DataTransfer()
    event.forEach((fileImage) => {
      dataTransfer.items.add(fileImage as File)
    })

    /*
     * Image Upload Logic:
     * 1. Find available slots that can accept new images (not master product images, not already filled)
     * 2. Limit upload to number of available slots (if 3 empty slots but 5 files, only first 3 are processed)
     * 3. Upload each file to its corresponding empty slot
     */

    const editableSlots = uploadImageHooks.filter((hook, index) => {
      const isMasterProductImage = productImages[index]?.isFromMasterProduct
      const hasImage =
        productImages[index]?.imageToken || productImages[index]?.id
      return hook?.status === "idle" && !isMasterProductImage && !hasImage
    })

    const filesToProcess = Array.from(dataTransfer.files).slice(
      0,
      editableSlots.length
    )

    filesToProcess.forEach((file, index) => {
      const slotDataTransfer = new DataTransfer()
      slotDataTransfer.items.add(file)
      editableSlots[index]?.handleUpload(slotDataTransfer.files)
    })

    if (errors.images?.message) {
      clearErrors("images")
    }
  }

  const handleOnDragImage = (fromIndex: number, toIndex: number) => {
    swap(fromIndex, toIndex)
  }

  const handleDeleteImage = (index: number) => {
    if (productImages[index]?.isFromMasterProduct) {
      return
    }

    remove(index)
    /* Reset all upload image hooks */
    uploadImageHooks.forEach((hook) => hook?.reset())
    if (errors.images?.message) {
      clearErrors("images")
    }
  }

  const handleProductVideoChange = (event: TFileVideo) => {
    if (errors.video?.message) {
      clearErrors("video")
    }
    const videoSource = event ? VideoSourceType.Internal : undefined
    const newFileList = convertToFileList([event])
    setValue("videoSource", videoSource)
    setValue("videoUrl", undefined)

    setValue("video", {
      name: event.name,
      size: event.size,
      value: event.value,
    })
    handleVideoUpload(newFileList)
  }

  const handleDeleteVideo = () => {
    resetVideoUpload()
    cancelVideoUpload()
    setValue("videoSource", undefined)
    setValue("videoUrl", "")
    setValue("videoToken", "")
    updateStoredProduct({
      videoToken: undefined,
      videoUrl: undefined,
      videoSource: undefined,
      video: { name: undefined, size: undefined, value: undefined },
    })
  }

  useEffect(() => {
    if (isSubmitSuccessful) {
      clearStoredProduct()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSubmitSuccessful])

  return (
    <SectionContainer title="Informasi Produk">
      <div className="mt-6">
        <ListSectoralProductField />
      </div>
      <div className="mt-6">
        <ProductNameField />
      </div>
      {!isCategoryTypeSpecial && (
        <>
          <div className="mt-6">
            <Fieldset
              description={`Gunakan format .jpg .jpeg .png dan ukuran optimal ${optimalWidth} x ${optimalHeight} px (Min. ${minWidth} x ${minHeight} px, Max. ${maxWidth} x ${maxHeight} px).\nPilih foto produk atau tarik dan letakkan hingga ${maxLimitImages} foto sekaligus di sini. Cantumkan min. ${minLimitImages} foto yang menarik agar produk semakin menarik pembeli.`}
              isMandatory
              name="Upload Foto Produk"
              id="images-container"
            >
              <UploadProductImageField
                uploadImageHooks={uploadImageHooks}
                onChange={handleProductImageChange}
                onError={handleErrorImage}
                onDelete={handleDeleteImage}
                value={productImages.map((img) => ({
                  ...img,
                  id: img.imageId,
                }))}
                onDragImage={handleOnDragImage}
                errorMessage={errors.images?.message}
                disabled={isConsolidationProduct}
                disableDrag={productImages.length <= 1}
              />
            </Fieldset>
          </div>
          <div className="mt-6">
            <Fieldset
              description="Format video .mp4 dan .mov. Disarankan durasi maks. 120 detik dan ukuran maks. 50MB."
              isMandatory={false}
              name="Video Produk"
            >
              <div className="flex w-full flex-col gap-1">
                <UploadProductVideoField
                  name={storedProduct.video?.name ?? getValues("video.name")}
                  size={storedProduct.video?.size ?? getValues("video.size")}
                  progressUpload={{
                    percentage: videoUploadPercentage,
                    timeLeft: videoUploadEstimatedTimeLeft,
                  }}
                  statusUpload={
                    videoUploadPercentage > 0 ? "uploading" : "idle"
                  }
                  value={
                    getValues("videoSource") === VideoSourceType.Internal
                      ? getValues("videoUrl") ||
                        storedProduct.video?.value ||
                        getValues("video.value")
                      : ""
                  }
                  onChange={handleProductVideoChange}
                  onDelete={handleDeleteVideo}
                  onError={handleErrorVideo}
                  onCancelUpload={handleDeleteVideo}
                  isDisabled={
                    getValues("videoSource") === VideoSourceType.External ||
                    isConsolidationProduct
                  }
                />
                {errors.video && (
                  <span className="mt-2 text-xs font-normal text-error500">
                    {errors.video?.message}
                  </span>
                )}
              </div>
            </Fieldset>
          </div>
          <div className="mt-6">
            <Fieldset isMandatory={false} name="URL Video Produk">
              <URLVideoField
                placeholder="Masukkan URL Video Youtube"
                disabled={isConsolidationProduct}
              />
            </Fieldset>
          </div>
        </>
      )}

      <div className="mt-6">
        <Fieldset
          description="Tulis penjelasan yang detail terkait produk agar mudah dimengerti dan ditemukan pembeli.\nDisarankan jangan memasukkan nomor HP, e-mail, dsb. ke dalam deskripsi produk untuk melindungi data pribadi Anda."
          name="Deskripsi"
        >
          <ProductDescriptionField />
        </Fieldset>
      </div>
    </SectionContainer>
  )
}

export default ProductInfoField
