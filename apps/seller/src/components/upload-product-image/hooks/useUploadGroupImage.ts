import { type ChangeEvent, type DragEvent, useMemo } from "react"
import { useRef, useState } from "react"
import { isValidFileName } from "shared-utils"

import { errorMessages } from "../constant"
import { type TFileImages } from "../type"
import { type TFormProductImages } from "@/app/products/form/fields/ProductInfoField/schema/productImagesSchema"
import validateImageDimensions from "@/utils/validateImageDimensions"

type TUseUploadGroupImage = {
  onChange: (file: TFileImages[] | null) => void
  accept?: string
  maxWidth: number
  maxHeight: number
  minWidth: number
  minHeight: number
  maxSize: number
  onCancel: () => void
  onError: (err: string) => void
  value: TFormProductImages | null
  maxLimitImages: number
  disabled?: boolean
}

type TFileValidationError = {
  fileName: string
  message: string
}

const useUploadGroupImage = ({
  onChange,
  accept,
  onCancel,
  maxSize,
  minWidth,
  minHeight,
  maxWidth,
  maxHeight,
  onError,
  value,
  maxLimitImages,
  disabled,
}: TUseUploadGroupImage) => {
  const [dragActive, setDragActive] = useState<boolean>(false)
  const [isError, setIsError] = useState<boolean>(false)

  const inputRef = useRef<HTMLInputElement>(null)

  const allowedFile: string[] = useMemo(() => {
    let allowFileTemp: string[] = []
    if (!accept) return allowFileTemp

    if (accept.startsWith(".")) {
      const preFormatted = accept.replace(/[,]/g, "")
      const addImageType = preFormatted.replace(/\./g, "image/")

      allowFileTemp = addImageType.split(" ")
    } else {
      allowFileTemp = accept.split(",")
    }

    return allowFileTemp
  }, [accept])

  const handleDragEnter = (
    e: DragEvent<HTMLDivElement | HTMLLabelElement | HTMLLabelElement>
  ) => {
    e.preventDefault()
    e.stopPropagation()

    const type = e.type

    if (type === "dragenter" || type === "dragover") {
      setDragActive(true)
      setIsError(false)
    }
  }

  const handleFile = (files: File[]) => {
    if (!files.length) return
    if (dragActive || disabled) return

    const finalFile: TFileImages[] = []
    files.forEach((file) => {
      const temp = file as TFileImages
      temp.id = Math.random().toString(36).substring(7)
      temp.value = URL.createObjectURL(file)
      finalFile.push(temp)
    })

    onChange(finalFile)

    if (inputRef.current) {
      inputRef.current.value = ""
    }
  }

  async function handleValidation(files: FileList): Promise<void> {
    const maxSizeInBytes = maxSize * 1024 * 1024
    const errors: TFileValidationError[] = []
    const validFiles: File[] = []
    const existingFileNames = value ? value.map((file) => file.name) : []

    const validateFile = async (file: File): Promise<void> => {
      if (existingFileNames.includes(file.name)) {
        errors.push({
          fileName: file.name,
          message: errorMessages.duplicateError,
        })
        return
      }

      if (!allowedFile.includes(file.type)) {
        errors.push({
          fileName: file.name,
          message: errorMessages.formatError(accept || ""),
        })
        return
      }

      if (!isValidFileName(file.name)) {
        errors.push({
          fileName: file.name,
          message: errorMessages.fileNameError,
        })
        return
      }

      if (file.size > maxSizeInBytes) {
        errors.push({
          fileName: file.name,
          message: errorMessages.sizeError(maxSize),
        })
        return
      }

      try {
        await validateImageDimensions({
          file,
          maxHeight,
          maxWidth,
          minHeight,
          minWidth,
        })
        validFiles.push(file)
      } catch (error) {
        errors.push({
          fileName: file.name,
          message: error instanceof Error ? error.message : String(error),
        })
      }
    }

    await Promise.all(Array.from(files).map(validateFile))

    if (value && validFiles.length + value.length > maxLimitImages) {
      errors.push({
        fileName: "",
        message: errorMessages.maxLimitError(maxLimitImages),
      })
    }

    if (errors.length > 0) {
      setIsError(true)
      onError(errors?.[0]?.message || "")
    } else {
      handleFile(validFiles)
    }
  }

  const handleDragLeave = (e: DragEvent<HTMLDivElement | HTMLLabelElement>) => {
    e.preventDefault()
    e.stopPropagation()

    const type = e.type

    type === "dragleave" && setDragActive(false)
    setIsError(false)
  }

  const handleDrop = (e: DragEvent<HTMLDivElement | HTMLLabelElement>) => {
    e.preventDefault()

    setDragActive(false)
    if (e.dataTransfer.files && e.dataTransfer.files.length) {
      setIsError(false)
      handleValidation(e.dataTransfer.files)
    }
  }

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    const { files } = e.target

    if (files && files.length) {
      setIsError(false)
      handleValidation(files)
    }
  }

  const handleClickCancel = () => {
    onChange(null)
    onCancel()
  }

  return {
    isError,
    dragActive,
    inputRef,
    handleChange,
    handleClickCancel,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
    handleFile,
    handleValidation,
  }
}

export default useUploadGroupImage
