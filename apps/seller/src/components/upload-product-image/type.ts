import { type TGetSignedUrl } from "shared-utils"

import { type TFormProductImages } from "@/app/products/form/fields/ProductInfoField/schema/productImagesSchema"

export type TUploadGroupImageProps = {
  maxLimitImages?: number
  accept?: string
  error: string
  isFailedUpload: boolean
  maxWidth: number
  maxHeight: number
  minWidth: number
  minHeight: number
  maxSize: number
  onCancel: () => void
  onChange: (file: TFileImages[]) => void
  onDelete: (index: number) => void
  onError: (err: string) => void
  onPreviewFile: () => void
  onDragImage: (fromIndex: number, toIndex: number) => void
  progress: number[]
  timeRemaining: number
  value: TFormProductImages
  isUploading: boolean
  isSingleImage?: boolean
  disabled?: boolean
  disableDrag?: boolean
  uploadImageHooks?: {
    file?: File
    percentage: number
    handleUpload: (paramFiles: FileList | null) => void
    cancel: () => void
    status: "error" | "idle" | "loading" | "success"
    signedUrlInfo?: TGetSignedUrl
  }[]
}

export type TFileImages = { id: string; value?: string } & File

export type CardImageDraggableProps = {
  id: string
  file: TFormProductImages[number]
  onMoveImage: (id: string, toIndex: number) => void
  onFindImage: (id: string) => number
  onDelete: (index: number) => void
  originalIndex: number
  error?: string
  disabled?: boolean
  isProtected?: boolean
  tooltip?: string
  disableDrag?: boolean
}

export type TDragDropItem = {
  id: string
  index: number
}
