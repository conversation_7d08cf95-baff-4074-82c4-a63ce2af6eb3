"use client"

import { cx } from "class-variance-authority"
import Image from "next/image"
import { useDrag, useDrop } from "react-dnd"
import { Trash } from "react-feather"

import { type CardImageDraggableProps } from "./type"
// TODO: Improve smooth transition
const CardImageDraggable = ({
  id,
  file,
  onMoveImage,
  onFindImage,
  onDelete,
  originalIndex,
  error,
  disabled = false,
  isProtected = false,
  disableDrag = false,
}: CardImageDraggableProps) => {
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: "image",
      item: { id, originalIndex },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
      canDrag: !disabled && !disableDrag,
    }),
    [id, originalIndex, disabled, disableDrag]
  )

  const [, drop] = useDrop(
    () => ({
      accept: "image",
      hover({ id: draggedId }: { id: string }) {
        if (draggedId !== id) {
          const toIndex = onFindImage(id)
          onMoveImage(draggedId, toIndex)
        }
      },
    }),
    [id, onFindImage, onMoveImage]
  )

  return (
    <div
      ref={(node) => drag(drop(node))}
      className={cx(
        "relative h-[133px] w-[133px] rounded-4 border border-solid",
        {
          "border-error500": !!error && !disabled,
          "border-grey50": !error,
          "cursor-not-allowed": disabled || disableDrag,
          "cursor-grab": !disabled && !disableDrag,
          "opacity-50": isDragging,
        }
      )}
      data-test="card-image-draggable"
    >
      <div
        role="Handle"
        ref={drag}
        className="relative flex h-full items-center justify-center rounded-4"
      >
        {!disabled && !isProtected && (
          <div
            className="absolute z-50 flex h-full w-full items-center justify-center rounded-4 text-primary500 opacity-0 hover:cursor-grabbing hover:bg-[#F1F3F7] hover:bg-opacity-70 hover:opacity-100"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
            }}
          >
            <button
              type="button"
              className="rounded-4 border border-solid border-primary500 bg-primary25 px-3 py-3"
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                onDelete(originalIndex)
              }}
            >
              <Trash size={16} className="cursor-pointer" />
            </button>
          </div>
        )}
        {originalIndex === 0 && (
          <p className="absolute left-2 top-2 rounded-2 bg-error25 px-2 py-1 text-center text-xs font-semibold text-error500">
            Foto Utama
          </p>
        )}
        {!!file?.imageUrl && (
          <Image
            alt="preview"
            src={file.imageUrl}
            width={133}
            height={133}
            className={cx("h-[131px] w-[131px] rounded-4", {
              "brightness-75": isProtected,
            })}
          />
        )}
      </div>
    </div>
  )
}

export default CardImageDraggable
