"use client"

import { cx } from "class-variance-authority"
import dynamic from "next/dynamic"
import React, { type ReactNode, useCallback, useRef } from "react"
import { useDragDropManager } from "react-dnd"
import { Image as ImageIcon, Plus } from "react-feather"
import { Spinner } from "shared-ui"

import useUploadGroupImage from "./hooks/useUploadGroupImage"
import { type TUploadGroupImageProps } from "./type"
import ProgressBar from "../progress-bar"

const CardImageDraggable = dynamic(() => import("./CardImageDraggable"), {
  loading: () => (
    <div
      className={cx(
        "h-[133px] w-[133px] cursor-not-allowed rounded-4 border border-solid border-grey50 bg-[#F1F3F7]"
      )}
      data-test="loading-image-component"
    >
      <div className="flex h-full flex-col items-center justify-center gap-y-2">
        <Spinner size="small" color="secondary" />
      </div>
    </div>
  ),
})

const EmptyImage = ({
  name,
  Icon,
  error,
  disabled = false,
}: {
  name: string
  Icon: ReactNode
  error?: string
  disabled?: boolean
}) => {
  return (
    <div
      className={cx("h-[133px] w-[133px] rounded-4 border border-solid", {
        "border-error500": !!error && !disabled,
        "border-grey50": !error,
        "bg-[#F1F3F7]": !disabled,
        "cursor-not-allowed": disabled,
        "cursor-pointer": !disabled,
      })}
      data-test="empty-image-component"
    >
      <div className="flex h-full flex-col items-center justify-center gap-y-2">
        {Icon && <div className={cx({ "opacity-50": disabled })}>{Icon}</div>}
        <p
          className={cx(
            "text-center text-disabled",
            disabled && "cursor-not-allowed"
          )}
        >
          {name}
        </p>
      </div>
    </div>
  )
}

const UploadProductImage = ({
  accept,
  maxHeight,
  maxWidth,
  minHeight,
  minWidth,
  maxSize,
  value,
  onError,
  onChange,
  onDragImage,
  onDelete,
  onCancel,
  maxLimitImages = 5,
  error,
  uploadImageHooks,
  disabled = false,
}: TUploadGroupImageProps) => {
  const dragDropManager = useDragDropManager()
  const monitor = dragDropManager.getMonitor()
  const isDragAndDropActive = monitor.getSourceClientOffset()
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  const { handleDragEnter, handleDragLeave, handleDrop, handleChange } =
    useUploadGroupImage({
      accept,
      maxHeight,
      maxWidth,
      minWidth,
      minHeight,
      maxSize,
      value,
      onError,
      maxLimitImages,
      disabled,
      onCancel,
      onChange: (files) => {
        if (!files || isDragAndDropActive || disabled) return
        onChange(files)
      },
    })

  const handleFindImageDraggable = useCallback(
    (id: string) => value?.findIndex((val) => val?.id === id) ?? -1,
    [value]
  )

  const handleMoveImage = useCallback(
    (id: string, toIndex: number) => {
      if (disabled) return
      const isAnyUploading = uploadImageHooks?.some(
        (hook) => hook?.status === "loading"
      )
      if (isAnyUploading) return

      const fromIndex = handleFindImageDraggable(id)
      if (fromIndex !== -1) {
        onDragImage(fromIndex, toIndex)
      }
    },
    [handleFindImageDraggable, onDragImage, disabled, uploadImageHooks]
  )

  const handleEmptyImageClick = (index: number) => {
    if (!disabled && inputRefs.current[index]) {
      inputRefs.current[index]?.click()
    }
  }

  return (
    <div className="w-full flex-col">
      <div
        id="imgDropArea"
        className={cx("flex min-h-[133px] w-10/12 flex-wrap gap-4 font-thin", {
          "cursor-not-allowed": disabled,
        })}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        data-test="label-upload-group-image"
      >
        {uploadImageHooks?.map((item, index) => {
          const hasProgress = item?.percentage ?? 0 > 0
          const isRenderDefaultImage = index === 0

          if (value[index] === undefined)
            return (
              <div key={index} className="relative">
                <input
                  accept={accept}
                  type="file"
                  multiple
                  autoComplete="off"
                  tabIndex={-1}
                  hidden
                  className="w-full"
                  ref={(el) => (inputRefs.current[index] = el)}
                  onChange={handleChange}
                  disabled={(value?.length ?? 0) >= maxLimitImages || disabled}
                  max={maxLimitImages - (value?.length ?? 0)}
                  data-test={`input-upload-group-image-${index}`}
                />
                <div
                  className="w-full"
                  onClick={() => handleEmptyImageClick(index)}
                >
                  <EmptyImage
                    name={
                      hasProgress
                        ? ""
                        : isRenderDefaultImage
                        ? "Foto Utama"
                        : `Foto ${index + 1}`
                    }
                    Icon={
                      hasProgress ? (
                        <div className="w-16 px-2 pt-4">
                          <ProgressBar
                            value={uploadImageHooks[index]?.percentage}
                            variant="vertical"
                          />
                        </div>
                      ) : isRenderDefaultImage ? (
                        <ImageIcon size={32} className="text-disabled" />
                      ) : (
                        <Plus size={32} className="text-disabled" />
                      )
                    }
                    error={error}
                    disabled={disabled}
                  />
                </div>
              </div>
            )
          return (
            <CardImageDraggable
              key={value[index]?.id ?? index}
              id={value[index]?.id ?? ""}
              file={value[index] ?? {}}
              onMoveImage={handleMoveImage}
              onFindImage={handleFindImageDraggable}
              onDelete={onDelete}
              originalIndex={index}
              error={error}
              disabled={disabled}
              isProtected={Boolean(value[index]?.isFromMasterProduct)}
              disableDrag={value?.length === 1}
            />
          )
        })}
      </div>
      {!!error && <div className="mt-2 text-xs text-error500">{error}</div>}
    </div>
  )
}

export default UploadProductImage
