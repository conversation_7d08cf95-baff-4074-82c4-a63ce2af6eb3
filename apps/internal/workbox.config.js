const { InjectManifest } = require('workbox-webpack-plugin')
const path = require('path')

function withWorkbox(nextConfig = {}) {
  return {
    ...nextConfig,
    webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
      // Only add Workbox in production client builds
      if (!dev && !isServer) {
        config.plugins.push(
          new InjectManifest({
            swSrc: path.join(__dirname, 'src/sw/graphql-sw.js'),
            swDest: 'static/sw.js',
            exclude: [
              /\.map$/,
              /manifest$/,
              /\.htaccess$/,
              /_next\/static\/chunks\/pages\/.*\.js$/,
              /_next\/static\/chunks\/.*\.js$/,
              /service-worker\.js$/,
              /sw\.js$/,
            ],
            modifyURLPrefix: {
              'static/': '/_next/static/',
            },
            additionalManifestEntries: [
              {
                url: '/',
                revision: buildId,
              },
            ],
          })
        )
      }

      // Call the original webpack config function if it exists
      if (typeof nextConfig.webpack === 'function') {
        return nextConfig.webpack(config, { buildId, dev, isServer, defaultLoaders, webpack })
      }

      return config
    },
  }
}

module.exports = { withWorkbox }
