// GraphQL Service Worker for Internal App
// Inspired by https://raw.githubusercontent.com/jonchenn/sw-graphql/refs/heads/master/public/sw.js

// Import IndexedDB library
importScripts('https://cdn.jsdelivr.net/npm/idb@8.0.0/build/umd.js')

// GraphQL Cache Configuration
const CACHE_CONFIG = {
  dbName: 'GraphQL-Cache-Internal',
  storeName: 'graphql-cache',
  maxAge: 3600, // 1 hour in seconds
  maxEntries: 100,
  enabled: true,
  excludeOperations: [
    // Add mutation operation names that should not be cached
    'CreateOrder',
    'UpdateOrder', 
    'DeleteOrder',
    'CreateUser',
    'UpdateUser',
    'DeleteUser'
  ]
}

// Initialize IndexedDB
let db = null

async function initDB() {
  if (db) return db
  
  try {
    db = await idb.openDB(CACHE_CONFIG.dbName, 1, {
      upgrade(database) {
        if (!database.objectStoreNames.contains(CACHE_CONFIG.storeName)) {
          const store = database.createObjectStore(CACHE_CONFIG.storeName, { keyPath: 'hash' })
          store.createIndex('timestamp', 'timestamp')
        }
      }
    })
    return db
  } catch (error) {
    console.error('Failed to initialize GraphQL cache database:', error)
    return null
  }
}

// Generate hash for GraphQL request (simple hash function)
function generateHash(request) {
  const normalizedRequest = {
    query: request.query.replace(/\s+/g, ' ').trim(),
    variables: request.variables || {},
    operationName: request.operationName
  }
  
  const str = JSON.stringify(normalizedRequest)
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36)
}

// Check if request should be cached
function shouldCache(request) {
  if (!CACHE_CONFIG.enabled) return false

  // Only cache queries, not mutations/subscriptions
  const trimmedQuery = request.query.trim()
  if (!trimmedQuery.toLowerCase().startsWith('query')) return false

  // Check exclude list
  if (request.operationName && CACHE_CONFIG.excludeOperations.includes(request.operationName)) {
    return false
  }

  return true
}

// Get cached response
async function getCachedResponse(request) {
  if (!shouldCache(request) || !db) return null

  try {
    const hash = generateHash(request)
    const entry = await db.get(CACHE_CONFIG.storeName, hash)
    
    if (!entry) return null

    // Check if cache is expired
    const now = Date.now()
    if (now - entry.timestamp > CACHE_CONFIG.maxAge * 1000) {
      await db.delete(CACHE_CONFIG.storeName, hash)
      return null
    }

    console.log('GraphQL cache hit:', request.operationName || 'anonymous')
    
    return new Response(JSON.stringify(entry.response.body), {
      status: entry.response.status,
      statusText: entry.response.statusText,
      headers: entry.response.headers
    })
  } catch (error) {
    console.error('Error retrieving from GraphQL cache:', error)
    return null
  }
}

// Cache response
async function cacheResponse(request, response) {
  if (!shouldCache(request) || !db) return

  try {
    const hash = generateHash(request)
    const responseClone = response.clone()
    
    // Serialize headers
    const headers = {}
    responseClone.headers.forEach((value, key) => {
      headers[key] = value
    })

    const body = await responseClone.json()
    
    const entry = {
      query: request.query,
      variables: request.variables,
      operationName: request.operationName,
      response: {
        headers,
        status: responseClone.status,
        statusText: responseClone.statusText,
        body
      },
      timestamp: Date.now(),
      hash
    }

    await db.put(CACHE_CONFIG.storeName, entry)
    
    // Clean up old entries
    await cleanupCache()
    
    console.log('GraphQL response cached:', request.operationName || 'anonymous')
  } catch (error) {
    console.error('Error storing in GraphQL cache:', error)
  }
}

// Cleanup old cache entries
async function cleanupCache() {
  if (!db) return

  try {
    const tx = db.transaction(CACHE_CONFIG.storeName, 'readonly')
    const count = await tx.store.count()
    
    if (count <= CACHE_CONFIG.maxEntries) return

    // Get all entries sorted by timestamp (oldest first)
    const entries = await db.getAllFromIndex(CACHE_CONFIG.storeName, 'timestamp')
    const entriesToDelete = entries.slice(0, count - CACHE_CONFIG.maxEntries)
    
    const deleteTx = db.transaction(CACHE_CONFIG.storeName, 'readwrite')
    for (const entry of entriesToDelete) {
      await deleteTx.store.delete(entry.hash)
    }
    await deleteTx.done
  } catch (error) {
    console.error('Error cleaning up GraphQL cache:', error)
  }
}

// Stale-while-revalidate strategy for GraphQL
async function staleWhileRevalidate(event) {
  const request = event.request.clone()
  
  try {
    const body = await request.json()
    const graphqlRequest = {
      query: body.query,
      variables: body.variables,
      operationName: body.operationName
    }

    const cachedResponse = await getCachedResponse(graphqlRequest)
    
    // Start network request in background
    const networkPromise = fetch(event.request.clone())
      .then(async (response) => {
        if (response.ok) {
          await cacheResponse(graphqlRequest, response.clone())
        }
        return response
      })
      .catch((error) => {
        console.error('Background network request failed:', error)
        return null
      })

    // Return cached response immediately if available
    if (cachedResponse) {
      event.waitUntil(networkPromise)
      return cachedResponse
    }

    // If no cache, wait for network
    const networkResponse = await networkPromise
    if (networkResponse) {
      return networkResponse
    }

    throw new Error('Both cache and network failed')
  } catch (error) {
    console.error('Error in GraphQL handler:', error)
    return fetch(event.request)
  }
}

// Service Worker event listeners
self.addEventListener('install', (event) => {
  console.log('GraphQL Service Worker installing...')
  event.waitUntil(
    initDB().then(() => {
      console.log('GraphQL cache initialized')
      return self.skipWaiting()
    })
  )
})

self.addEventListener('activate', (event) => {
  console.log('GraphQL Service Worker activating...')
  event.waitUntil(self.clients.claim())
})

// Handle fetch events
self.addEventListener('fetch', (event) => {
  // Only handle POST requests to GraphQL endpoints
  if (
    event.request.method === 'POST' && 
    (event.request.url.includes('/graphql') || event.request.url.includes('/api/graphql'))
  ) {
    event.respondWith(staleWhileRevalidate(event))
  }
  // Let other requests pass through normally
})

// Handle messages from main thread
self.addEventListener('message', (event) => {
  const { type } = event.data

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
    case 'GET_CACHE_STATS':
      getCacheStats().then(stats => {
        event.ports[0].postMessage(stats)
      })
      break
    case 'CLEAR_CACHE':
      clearCache().then(() => {
        event.ports[0].postMessage({ success: true })
      })
      break
  }
})

async function getCacheStats() {
  if (!db) return { count: 0, size: 0 }
  
  try {
    const entries = await db.getAll(CACHE_CONFIG.storeName)
    const size = JSON.stringify(entries).length
    return { count: entries.length, size }
  } catch (error) {
    console.error('Error getting cache stats:', error)
    return { count: 0, size: 0 }
  }
}

async function clearCache() {
  if (!db) return
  
  try {
    await db.clear(CACHE_CONFIG.storeName)
    console.log('GraphQL cache cleared')
  } catch (error) {
    console.error('Error clearing cache:', error)
  }
}
