/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation.
 * This is especially useful for Docker builds.
 */
!process.env.SKIP_ENV_VALIDATION && (await import("./src/env/server.mjs"))
import { withSentryConfig } from "@sentry/nextjs"
import config from "shared-config/next-config.js"
import { withWorkbox } from "./workbox.config.js"

export default withSentryConfig(withWorkbox(config))
