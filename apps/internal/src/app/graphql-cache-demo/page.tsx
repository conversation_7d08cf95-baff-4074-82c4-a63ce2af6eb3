"use client"

import { useState } from "react"

interface QueryResult {
  data?: unknown
  errors?: Array<{
    message: string
    locations?: Array<{ line: number; column: number }>
    path?: Array<string | number>
  }>
  error?: string
}

interface GraphQLCacheAPI {
  getCacheStats: () => Promise<{ count: number; size: number } | null>
  clearCache: () => Promise<boolean | undefined>
  isRegistered: boolean
  isUpdateAvailable: boolean
  update: () => void
}

declare global {
  interface Window {
    graphqlCache?: GraphQLCacheAPI
  }
}

export default function GraphQLCacheDemoPage() {
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [cacheStats, setCacheStats] = useState<{
    count: number
    size: number
  } | null>(null)

  const testQuery = `
    query GetUsers {
      users {
        id
        name
        email
      }
    }
  `

  const executeGraphQLQuery = async () => {
    setLoading(true)
    try {
      const response = await fetch("/graphql", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: testQuery,
          variables: {},
          operationName: "GetUsers",
        }),
      })

      const result = (await response.json()) as QueryResult
      setQueryResult(result)
    } catch (error) {
      console.error("GraphQL query failed:", error)
      setQueryResult({
        error: error instanceof Error ? error.message : "Unknown error",
      })
    } finally {
      setLoading(false)
    }
  }

  const getCacheStats = async () => {
    try {
      // Access the global cache management functions
      const stats = await window.graphqlCache?.getCacheStats()
      setCacheStats(stats || null)
    } catch (error) {
      console.error("Failed to get cache stats:", error)
    }
  }

  const clearCache = async () => {
    try {
      const success = await window.graphqlCache?.clearCache()
      if (success) {
        alert("Cache cleared successfully!")
        setCacheStats({ count: 0, size: 0 })
      }
    } catch (error) {
      console.error("Failed to clear cache:", error)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="mb-8 text-3xl font-bold">GraphQL Cache Demo</h1>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        {/* Query Testing */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="mb-4 text-xl font-semibold">Test GraphQL Query</h2>

          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium">Query:</label>
            <pre className="bg-gray-100 rounded overflow-x-auto p-3 text-sm">
              {testQuery}
            </pre>
          </div>

          <button
            onClick={executeGraphQLQuery}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded px-4 py-2"
          >
            {loading ? "Executing..." : "Execute Query"}
          </button>

          {queryResult && (
            <div className="mt-4">
              <label className="mb-2 block text-sm font-medium">Result:</label>
              <pre className="bg-gray-100 rounded max-h-64 overflow-x-auto overflow-y-auto p-3 text-sm">
                {JSON.stringify(queryResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Cache Management */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="mb-4 text-xl font-semibold">Cache Management</h2>

          <div className="space-y-4">
            <button
              onClick={getCacheStats}
              className="bg-green-500 hover:bg-green-600 text-white rounded w-full px-4 py-2"
            >
              Get Cache Stats
            </button>

            <button
              onClick={clearCache}
              className="bg-red-500 hover:bg-red-600 text-white rounded w-full px-4 py-2"
            >
              Clear Cache
            </button>

            {cacheStats && (
              <div className="bg-gray-100 rounded p-3">
                <h3 className="mb-2 font-medium">Cache Statistics:</h3>
                <p>Entries: {cacheStats.count}</p>
                <p>Size: {(cacheStats.size / 1024).toFixed(2)} KB</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 rounded-lg mt-8 p-6">
        <h2 className="mb-4 text-xl font-semibold">How to Test:</h2>
        <ol className="list-inside list-decimal space-y-2">
          <li>Open browser DevTools and go to the Network tab</li>
          <li>
            Click &quot;Execute Query&quot; - you should see a network request
            to /graphql
          </li>
          <li>
            Click &quot;Execute Query&quot; again - the second request should be
            served from cache (no network request)
          </li>
          <li>Check the Console for cache hit/miss messages</li>
          <li>
            Use &quot;Get Cache Stats&quot; to see how many entries are cached
          </li>
          <li>Use &quot;Clear Cache&quot; to reset the cache</li>
        </ol>
      </div>

      {/* Service Worker Status */}
      <div className="bg-blue-50 rounded-lg mt-8 p-6">
        <h2 className="mb-4 text-xl font-semibold">Service Worker Status:</h2>
        <p>
          Service Worker:{" "}
          {window.graphqlCache?.isRegistered
            ? "✅ Registered"
            : "❌ Not Registered"}
        </p>
        <p className="text-gray-600 mt-2 text-sm">
          Note: Service Worker only works in production builds. In development,
          queries will not be cached.
        </p>
      </div>
    </div>
  )
}
