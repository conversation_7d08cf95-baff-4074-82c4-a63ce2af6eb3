"use client"

import { useState } from 'react'

export default function GraphQLCacheDemoPage() {
  const [queryResult, setQueryResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [cacheStats, setCacheStats] = useState<{ count: number; size: number } | null>(null)

  const testQuery = `
    query GetUsers {
      users {
        id
        name
        email
      }
    }
  `

  const executeGraphQLQuery = async () => {
    setLoading(true)
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: testQuery,
          variables: {},
          operationName: 'GetUsers'
        })
      })

      const result = await response.json()
      setQueryResult(result)
    } catch (error) {
      console.error('GraphQL query failed:', error)
      setQueryResult({ error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const getCacheStats = async () => {
    try {
      // Access the global cache management functions
      const stats = await (window as any).graphqlCache?.getCacheStats()
      setCacheStats(stats)
    } catch (error) {
      console.error('Failed to get cache stats:', error)
    }
  }

  const clearCache = async () => {
    try {
      const success = await (window as any).graphqlCache?.clearCache()
      if (success) {
        alert('Cache cleared successfully!')
        setCacheStats({ count: 0, size: 0 })
      }
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">GraphQL Cache Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Query Testing */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Test GraphQL Query</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Query:</label>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
              {testQuery}
            </pre>
          </div>

          <button
            onClick={executeGraphQLQuery}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded"
          >
            {loading ? 'Executing...' : 'Execute Query'}
          </button>

          {queryResult && (
            <div className="mt-4">
              <label className="block text-sm font-medium mb-2">Result:</label>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto max-h-64 overflow-y-auto">
                {JSON.stringify(queryResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Cache Management */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Cache Management</h2>
          
          <div className="space-y-4">
            <button
              onClick={getCacheStats}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded w-full"
            >
              Get Cache Stats
            </button>

            <button
              onClick={clearCache}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded w-full"
            >
              Clear Cache
            </button>

            {cacheStats && (
              <div className="bg-gray-100 p-3 rounded">
                <h3 className="font-medium mb-2">Cache Statistics:</h3>
                <p>Entries: {cacheStats.count}</p>
                <p>Size: {(cacheStats.size / 1024).toFixed(2)} KB</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-8 bg-yellow-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">How to Test:</h2>
        <ol className="list-decimal list-inside space-y-2">
          <li>Open browser DevTools and go to the Network tab</li>
          <li>Click "Execute Query" - you should see a network request to /graphql</li>
          <li>Click "Execute Query" again - the second request should be served from cache (no network request)</li>
          <li>Check the Console for cache hit/miss messages</li>
          <li>Use "Get Cache Stats" to see how many entries are cached</li>
          <li>Use "Clear Cache" to reset the cache</li>
        </ol>
      </div>

      {/* Service Worker Status */}
      <div className="mt-8 bg-blue-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Service Worker Status:</h2>
        <p>
          Service Worker: {(window as any).graphqlCache?.isRegistered ? '✅ Registered' : '❌ Not Registered'}
        </p>
        <p className="text-sm text-gray-600 mt-2">
          Note: Service Worker only works in production builds. In development, queries will not be cached.
        </p>
      </div>
    </div>
  )
}
