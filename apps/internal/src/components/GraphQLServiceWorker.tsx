"use client"

import { useEffect, useState, useCallback } from "react"

interface GraphQLServiceWorkerProps {
  children?: React.ReactNode
}

export default function GraphQLServiceWorker({
  children,
}: GraphQLServiceWorkerProps) {
  const [isRegistered, setIsRegistered] = useState(false)
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false)
  const [registration, setRegistration] =
    useState<ServiceWorkerRegistration | null>(null)

  useEffect(() => {
    if (typeof window === "undefined" || !("serviceWorker" in navigator)) {
      console.warn("Service Worker not supported")
      return
    }

    // Only register in production
    if (process.env.NODE_ENV !== "production") {
      console.log("Service Worker registration skipped in development")
      return
    }

    const registerServiceWorker = async () => {
      try {
        const reg = await navigator.serviceWorker.register("/graphql-sw.js", {
          scope: "/",
          updateViaCache: "none",
        })

        setRegistration(reg)
        setIsRegistered(true)
        console.log("GraphQL Service Worker registered successfully")

        // Check for updates
        reg.addEventListener("updatefound", () => {
          const newWorker = reg.installing
          if (newWorker) {
            newWorker.addEventListener("statechange", () => {
              if (
                newWorker.state === "installed" &&
                navigator.serviceWorker.controller
              ) {
                setIsUpdateAvailable(true)
              }
            })
          }
        })

        // Listen for controlling service worker changes
        navigator.serviceWorker.addEventListener("controllerchange", () => {
          window.location.reload()
        })
      } catch (error) {
        console.error("GraphQL Service Worker registration failed:", error)
      }
    }

    registerServiceWorker()
  }, [])

  const handleUpdate = useCallback(async () => {
    if (!registration) return

    try {
      // Tell the waiting service worker to skip waiting
      if (registration.waiting) {
        registration.waiting.postMessage({ type: "SKIP_WAITING" })
      }
      setIsUpdateAvailable(false)
    } catch (error) {
      console.error("Error updating service worker:", error)
    }
  }, [registration])

  const clearCache = useCallback(async () => {
    if (!registration || !registration.active) return

    try {
      // Create a message channel for communication
      const messageChannel = new MessageChannel()

      return new Promise<boolean>((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          const data = event.data as { success?: boolean }
          if (data.success) {
            console.log("GraphQL cache cleared successfully")
            resolve(true)
          } else {
            resolve(false)
          }
        }

        if (registration.active) {
          registration.active.postMessage({ type: "CLEAR_CACHE" }, [
            messageChannel.port2,
          ])
        }
      })
    } catch (error) {
      console.error("Error clearing cache:", error)
      return false
    }
  }, [registration])

  const getCacheStats = useCallback(async () => {
    if (!registration || !registration.active) return null

    try {
      // Create a message channel for communication
      const messageChannel = new MessageChannel()

      return new Promise<{ count: number; size: number } | null>((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          const data = event.data as { count: number; size: number }
          console.log("GraphQL cache stats:", data)
          resolve(data)
        }

        if (registration.active) {
          registration.active.postMessage({ type: "GET_CACHE_STATS" }, [
            messageChannel.port2,
          ])
        }
      })
    } catch (error) {
      console.error("Error getting cache stats:", error)
      return null
    }
  }, [registration])

  // Expose cache management functions globally for debugging
  useEffect(() => {
    if (typeof window !== "undefined") {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ;(window as any).graphqlCache = {
        clearCache,
        getCacheStats,
        isRegistered,
        isUpdateAvailable,
        update: handleUpdate,
      }
    }
  }, [
    isRegistered,
    isUpdateAvailable,
    registration,
    clearCache,
    getCacheStats,
    handleUpdate,
  ])

  return (
    <>
      {children}
      {isUpdateAvailable && (
        <div
          style={{
            position: "fixed",
            bottom: "20px",
            right: "20px",
            background: "#007bff",
            color: "white",
            padding: "12px 16px",
            borderRadius: "8px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            zIndex: 9999,
            fontSize: "14px",
            cursor: "pointer",
          }}
          onClick={handleUpdate}
        >
          🔄 Update Available - Click to Refresh
        </div>
      )}
    </>
  )
}
