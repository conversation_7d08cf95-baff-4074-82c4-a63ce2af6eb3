{"name": "internal-app", "version": "0.2.0", "private": true, "scripts": {"build": "next build", "codegen": "graphql-codegen --config codegen.ts", "dev": "concurrently \"npm run codegen\" \"npm run dev-next\"", "format": "prettier --write \"**/*.{ts,tsx,md}\" \"!**/src/gql/*.{ts,tsx,md}\" \"!**/src/federatedGql/*.{ts,tsx,md}\"", "dev-next": "next dev -p 3002", "lint": "next lint --fix", "start": "next start -p 3002"}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.6.0", "@google-cloud/storage": "^6.9.3", "@graphql-typed-document-node/core": "*", "@hookform/resolvers": "^2.9.11", "@mdx-js/react": "^2.3.0", "@sendbird/chat": "^4.4.0", "@sendbird/uikit-react": "^3.3.7", "@sentry/nextjs": "*", "@tanstack/react-query": "^4.24.4", "@tanstack/react-table": "^8.7.9", "@types/lodash": "^4.14.197", "@types/lodash.debounce": "^4.0.7", "@unleash/nextjs": "^1.4.1", "autoprefixer": "^10.4.7", "axios": "^1.3.5", "class-variance-authority": "^0.6.0", "cypress": "^12.5.1", "dayjs": "^1.11.6", "fast-deep-equal": "^3.1.3", "graphql": "*", "graphql-request": "*", "gtp-ui": "workspace:*", "immutability-helper": "^3.1.1", "jsdom": "^25.0.1", "jsonwebtoken": "^9.0.0", "jwt-decode": "^3.1.2", "next": "*", "next-auth": "^4.24.5", "order-ui": "workspace:*", "path-to-regexp": "^6.2.1", "public-google-sheets-parser": "^1.3.2", "react": "*", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "*", "react-feather": "^2.0.10", "react-hook-form": "^7.43.1", "react-image-gallery": "^1.2.11", "react-number-format": "^5.1.4", "react-query-kit": "^2.0.7", "react-select": "^5.5.8", "shared-assets": "workspace:*", "shared-ui": "workspace:*", "shared-utils": "workspace:*", "tailwindcss": "*", "valid-filename": "^4.0.0", "socket.io": "*", "socket.io-client": "*", "workbox-webpack-plugin": "^7.0.0", "workbox-window": "^7.0.0", "ws": "^8.18.0", "zod": "*", "zustand": "^4.3.3"}, "devDependencies": {"@babel/core": "^7.0.0-0", "@graphql-codegen/cli": "*", "@graphql-codegen/client-preset": "*", "@graphql-typed-document-node/core": "*", "@total-typescript/ts-reset": "^0.3.7", "@types/jest": "^25.2.3", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^18.11.18", "@types/prettier": "^2.7.2", "@types/react": "18.0.26", "@types/react-dom": "18.0.10", "@types/react-image-gallery": "^1.2.0", "@typescript-eslint/eslint-plugin": "^5.47.1", "@typescript-eslint/parser": "^5.47.1", "@types/socket.io-client": "*", "@types/ws": "^8.5.12", "concurrently": "^7.6.0", "eslint": "^8.30.0", "eslint-config-next": "*", "husky": "^8.0.0", "postcss": "^8.4.14", "prettier": "^2.8.1", "prettier-plugin-tailwindcss": "^0.2.1", "shared-config": "workspace:*", "standard-version": "^9.5.0", "typescript": "*"}, "ct3aMetadata": {"initVersion": "7.6.0"}}