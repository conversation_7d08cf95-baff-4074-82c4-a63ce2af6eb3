# GraphQL Caching with Service Worker

This implementation provides GraphQL query caching using Service Workers and IndexedDB, inspired by [sw-graphql](https://github.com/jonchenn/sw-graphql).

## Features

- **Automatic GraphQL Query Caching**: Only queries are cached, mutations are excluded
- **IndexedDB Storage**: Persistent storage that survives browser restarts
- **Stale-While-Revalidate Strategy**: Returns cached data immediately while fetching fresh data in background
- **Configurable Cache Settings**: TTL, max entries, operation exclusions
- **Cache Management**: Clear cache and get statistics
- **Production-Only**: Service Worker only registers in production builds

## Architecture

### Components

1. **Service Worker** (`/public/graphql-sw.js`): Intercepts GraphQL requests and manages caching
2. **GraphQLServiceWorker Component**: Registers and manages the service worker
3. **Shared Utils**: Reusable caching utilities in `packages/shared-utils/src/graphql-cache/`

### Cache Strategy

- **Cache Hit**: Returns cached response immediately, updates cache in background
- **Cache Miss**: Fetches from network and caches the response
- **Cache Expiry**: Configurable TTL (default: 1 hour)
- **Cache Cleanup**: Automatically removes old entries when max limit is reached

## Configuration

### Service Worker Configuration

```javascript
const CACHE_CONFIG = {
  dbName: 'GraphQL-Cache-Internal',
  storeName: 'graphql-cache',
  maxAge: 3600, // 1 hour in seconds
  maxEntries: 100,
  enabled: true,
  excludeOperations: [
    'CreateOrder',
    'UpdateOrder', 
    'DeleteOrder',
    // Add more mutation names here
  ]
}
```

### What Gets Cached

- ✅ GraphQL queries (operations starting with `query`)
- ❌ GraphQL mutations (operations starting with `mutation`)
- ❌ GraphQL subscriptions (operations starting with `subscription`)
- ❌ Operations in the `excludeOperations` list

## Usage

### 1. Service Worker Registration

The service worker is automatically registered in production via the `GraphQLServiceWorker` component in `ClientProviders.tsx`.

### 2. Testing the Cache

Visit `/graphql-cache-demo` to test the caching functionality:

1. Execute a GraphQL query
2. Execute the same query again (should be served from cache)
3. Check cache statistics
4. Clear cache if needed

### 3. Debugging

Access cache management functions via browser console:

```javascript
// Get cache statistics
await window.graphqlCache.getCacheStats()

// Clear cache
await window.graphqlCache.clearCache()

// Check if service worker is registered
window.graphqlCache.isRegistered
```

## Development vs Production

### Development
- Service worker is **not registered**
- All requests go directly to the network
- No caching occurs

### Production
- Service worker is registered automatically
- GraphQL queries are cached according to configuration
- Cache management functions are available

## Cache Key Generation

Cache keys are generated based on:
- Query string (normalized, whitespace removed)
- Variables object
- Operation name

This ensures that the same query with different variables or different queries are cached separately.

## Browser Support

- **IndexedDB**: Required for persistent storage
- **Service Workers**: Required for request interception
- **Modern Browsers**: Chrome 45+, Firefox 44+, Safari 11.1+

## Monitoring

### Console Logs

The service worker logs cache operations:

```
GraphQL cache hit: GetUsers
GraphQL response cached: GetUsers
GraphQL cache initialized
```

### Network Tab

- Cache hits: No network request visible
- Cache misses: Network request visible
- Background updates: Additional network requests for cache updates

## Troubleshooting

### Service Worker Not Registering

1. Check if running in production mode
2. Verify `/graphql-sw.js` is accessible
3. Check browser console for registration errors

### Queries Not Being Cached

1. Verify the query starts with `query`
2. Check if operation name is in `excludeOperations`
3. Ensure the request URL contains `/graphql`

### Cache Not Persisting

1. Check if IndexedDB is supported
2. Verify browser storage permissions
3. Check for private/incognito mode restrictions

## Performance Considerations

- **Memory Usage**: Cache is stored in IndexedDB, not memory
- **Network Reduction**: Cached queries don't hit the network
- **Background Updates**: Fresh data is fetched in background
- **Automatic Cleanup**: Old entries are removed automatically

## Security Considerations

- **Same-Origin**: Service worker only intercepts same-origin requests
- **HTTPS Required**: Service workers require HTTPS in production
- **No Sensitive Data**: Avoid caching queries with sensitive information

## Future Enhancements

- [ ] Cache invalidation based on mutations
- [ ] Selective cache warming
- [ ] Cache compression
- [ ] Analytics and metrics
- [ ] Cache sharing between tabs
- [ ] Offline support with fallback responses
