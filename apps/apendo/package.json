{"name": "apendo", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev-next": "node --inspect ./node_modules/next/dist/bin/next dev -p 3008", "codegen": "graphql-codegen --config codegen.ts", "dev": "concurrently \"npm run codegen\" \"npm run dev-next\"", "lint": "next lint --fix", "start": "next start", "format": "prettier --write \"**/*.{ts,tsx,md}\" \"!**/src/gql/*.{ts,tsx,md}\" \"!**/src/federatedGql/*.{ts,tsx,md}\"", "release": "standard-version --skip.bump --skip.tag --skip.commit"}, "dependencies": {"@graphql-typed-document-node/core": "*", "@sentry/nextjs": "*", "cookies-next": "^2.1.1", "graphql-request": "^5.2.0", "jwt-decode": "^3.1.2", "next": "15.2.5", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "shared-config": "workspace:*", "shared-utils": "workspace:*", "zod": "*"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "*", "@graphql-codegen/client-preset": "*", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/prettier": "^2.7.2", "prettier": "^2.8.1", "prettier-plugin-tailwindcss": "^0.2.1", "concurrently": "^7.6.0", "eslint": "^9", "eslint-config-next": "15.2.5", "tailwindcss": "^4", "typescript": "^5"}}